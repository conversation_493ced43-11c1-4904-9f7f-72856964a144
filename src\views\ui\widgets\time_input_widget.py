import re
from PySide6.QtGui import QRegularExpressionValidator
from PySide6.QtCore import (
     QTime, 
     QRegularExpression
)
from PySide6.QtWidgets import (
    QWidget,
    QHBoxLayout,
    QPushButton,
    QSizePolicy,
    QTimeEdit,
    QComboBox
)

from services.language_utils.translations_functions import get_system_language
from views.utils.widget_style import WidgetStyle


class TimeInputWidget(QWidget):
    """
    Widget para entrada de horário com botão de remoção.
    Este widget é utilizado dentro do SchedulerDialog para permitir a adição e remoção de horários de execução.
    """
    def __init__(self, parent=None, can_delete=True, language=None):
        """
        Inicializa o widget de entrada de horário.

        Args:
            parent (QWidget, optional): Widget pai. Defaults to None.
            can_delete (bool, optional): Se o horário pode ser removido. Defaults to True.
            language (str, optional): Idioma atual da aplicação. Defaults to None.
        """
        super().__init__(parent)
        # Define o idioma com base no parâmetro recebido
        self.language = language if language else get_system_language()

        # Força o idioma para Português se não for explicitamente English
        if self.language != "English":
            self.language = "Português"

        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Se pode remover o horário, adiciona o botão
        if can_delete:
            self.remove_button = QPushButton("×")
            self.remove_button.setFixedSize(15, 15)
            self.remove_button.setStyleSheet(WidgetStyle().remove_button_style())
            self.remove_button.clicked.connect(self.try_delete)
            layout.addWidget(self.remove_button)

        # Dropdown para seleção de horário
        self.time_combo = QComboBox()
        self.time_combo.setEditable(True)
        self.time_combo.setFixedHeight(25)

        # Preenche o dropdown com as opções de horário baseado no idioma
        self.populate_time_options()

        # Define a largura com base no idioma
        if self.language == "English":
            self.time_combo.setFixedWidth(100)  # Largura maior para caber o AM/PM
        else:
            self.time_combo.setFixedWidth(70)

        # Adiciona um validador para permitir apenas números, dois pontos e AM/PM
        self.setup_validator()

        self.time_combo.setStyleSheet(WidgetStyle().edit_time_style())
        layout.addWidget(self.time_combo)

        # Mantém uma referência ao QTimeEdit para compatibilidade com o código existente
        self.time_edit = QTimeEdit()
        self.time_edit.setVisible(False)

        # Conecta o evento de mudança no combobox para atualizar o time_edit
        self.time_combo.currentTextChanged.connect(self.update_time_edit)

        # Conecta o evento de perda de foco para formatar o texto
        line_edit = self.time_combo.lineEdit()
        if line_edit:
            line_edit.editingFinished.connect(self.format_time_on_focus_out)

        # Adiciona um espaço (addStretch) para alinhar com o botão de remoção de horário
        layout.addStretch()
        self.setLayout(layout)

    def setup_validator(self):
        """
        Configura um validador para permitir apenas números, dois pontos e AM/PM no campo de horário.
        """
        line_edit = self.time_combo.lineEdit()
        if not line_edit:
            return

        if self.language == "English":
            # Não usamos validador para permitir edição livre
            # Em vez disso, vamos usar eventos para formatar o texto quando o usuário terminar de editar

            # Conecta o evento de edição para formatar o texto em tempo real
            # mas apenas para verificar o AM/PM
            line_edit.textEdited.connect(self.validate_time_input)
        else:
            # Para português, permite apenas números e dois pontos
            # Exemplo: "01:00", "23:59"
            regex = QRegularExpression("^[0-9]{1,2}(:[0-9]{1,2})?$")
            validator = QRegularExpressionValidator(regex)
            line_edit.setValidator(validator)

    def populate_time_options(self):
        """
        Preenche o dropdown com as opções de horário baseado no idioma.
        """
        self.time_combo.clear()

        # Força o idioma para Português se não for explicitamente English
        if self.language != "English":
            self.language = "Português"
            # Formato 24 horas para português (00:00, 01:00, 02:00, etc.)
            for hour in range(24):
                self.time_combo.addItem(f"{hour:02d}:00")
        else:
            # Formato 12 horas para inglês (01:00 AM, 02:00 AM, etc.) com zeros à esquerda e com minutos
            for hour in range(1, 13):
                self.time_combo.addItem(f"{hour:02d}:00 AM")
            for hour in range(1, 13):
                self.time_combo.addItem(f"{hour:02d}:00 PM")

    def update_time_edit(self, text):
        """
        Atualiza o QTimeEdit com base no texto selecionado no dropdown.
        Também formata o texto para o formato padrão se o usuário digitar apenas números.
        """
        # Força o idioma para Português se não for explicitamente English
        if self.language != "English":
            self.language = "Português"

        # Se o texto estiver vazio, não faz nada
        if not text.strip():
            return

        try:
            if self.language == "English":
                # Verifica se o texto contém AM/PM
                is_am = "am" in text.lower()
                is_pm = "pm" in text.lower()

                # Remove AM/PM para processamento
                clean_text = text.lower().replace("am", "").replace("pm", "").strip()

                # Processa o texto limpo
                if ":" in clean_text:
                    # Formato com horas e minutos (1:30, 01:30, etc.)
                    parts = clean_text.split(":")
                    if len(parts) >= 2 and parts[0].strip() and parts[1].strip():
                        hour = int(parts[0])
                        minute = int(parts[1])
                    else:
                        # Se faltam partes, usa valores padrão
                        hour = int(parts[0]) if parts[0].strip() else 0
                        minute = 0
                else:
                    # Apenas horas (1, 12, etc.)
                    try:
                        hour = int(clean_text) if clean_text.strip() else 0
                        minute = 0
                    except ValueError:
                        # Se não conseguir converter, tenta extrair apenas o número
                        match = re.search(r'(\d+)', clean_text)
                        if match:
                            hour = int(match.group(1))
                            minute = 0
                        else:
                            # Se não encontrar números, usa hora atual
                            current_time = self.time_edit.time()
                            hour = current_time.hour()
                            minute = current_time.minute()

                # Ajusta a hora com base em AM/PM explícito
                if is_pm and hour < 12:
                    hour += 12
                elif is_am and hour == 12:
                    hour = 0

                # Se não tem AM/PM explícito, mantém a hora como está
                # Isso permite que o usuário edite sem que o formato mude imediatamente

                # Atualiza o QTimeEdit com a hora processada
                self.time_edit.setTime(QTime(hour, minute))

                # Verifica se o foco está no combobox (usuário está editando)
                line_edit = self.time_combo.lineEdit()
                if line_edit and not line_edit.hasFocus():
                    # Usuário terminou de editar, formata o texto
                    # Determina se é AM ou PM com base na hora atual
                    if hour >= 12:
                        am_pm = "PM"
                        display_hour = hour - 12 if hour > 12 else 12
                    else:
                        am_pm = "AM"
                        display_hour = hour if hour > 0 else 12

                    # Formata para o padrão "01:00 AM"
                    formatted_text = f"{display_hour:02d}:{minute:02d} {am_pm}"

                    # Atualiza o texto apenas se for diferente
                    if text != formatted_text:
                        # Desconecta temporariamente o sinal para evitar recursão
                        self.time_combo.currentTextChanged.disconnect(self.update_time_edit)
                        self.time_combo.setCurrentText(formatted_text)
                        self.time_combo.currentTextChanged.connect(self.update_time_edit)
            else:
                # Formato 24 horas (01:00, 15:00, etc.)
                # Remove AM/PM se estiver presente (caso o usuário tenha digitado manualmente)
                clean_text = text.lower().replace("am", "").replace("pm", "").strip()

                if ":" in clean_text:
                    parts = clean_text.split(":")
                    if len(parts) == 2:
                        hour = int(parts[0])
                        minute = int(parts[1]) if parts[1] else 0
                        self.time_edit.setTime(QTime(hour, minute))
                else:
                    # Se não tem ":", assume que é apenas a hora
                    try:
                        hour = int(clean_text)
                        minute = 0
                        self.time_edit.setTime(QTime(hour, minute))

                        # Formata o texto para o padrão e atualiza o combobox
                        if not re.match(r"^[0-9]{2}:[0-9]{2}$", text):
                            formatted_text = f"{hour:02d}:{minute:02d}"

                            # Desconecta temporariamente o sinal para evitar recursão
                            self.time_combo.currentTextChanged.disconnect(self.update_time_edit)
                            self.time_combo.setCurrentText(formatted_text)
                            self.time_combo.currentTextChanged.connect(self.update_time_edit)
                    except ValueError:
                        # Se não conseguir converter, tenta extrair apenas o número
                        match = re.search(r'(\d+)', clean_text)
                        if match:
                            hour = int(match.group(1))
                            self.time_edit.setTime(QTime(hour, 0))
        except (ValueError, IndexError):
            # Se não conseguir converter, mantém o valor atual
            pass

    def setTime(self, time):
        """
        Define o horário no dropdown.

        Args:
            time (QTime): Objeto QTime com o horário a ser definido.
        """
        hour = time.hour()
        minute = time.minute()

        # Força o idioma para Português se não for explicitamente English
        if self.language == "Português":
            # Formato 24 horas para português
            self.time_combo.setCurrentText(f"{hour:02d}:{minute:02d}")
        else:
            # Formato 12 horas para inglês com zeros à esquerda
            if hour == 0:
                # Meia-noite (00:00) é 12:00 AM
                self.time_combo.setCurrentText(f"12:{minute:02d} AM")
            elif hour < 12:
                # Manhã (01:00 - 11:00) é 01:00 AM - 11:00 AM
                self.time_combo.setCurrentText(f"{hour:02d}:{minute:02d} AM")
            elif hour == 12:
                # Meio-dia (12:00) é 12:00 PM
                self.time_combo.setCurrentText(f"12:{minute:02d} PM")
            else:
                # Tarde/noite (13:00 - 23:00) é 01:00 PM - 11:00 PM
                self.time_combo.setCurrentText(f"{(hour-12):02d}:{minute:02d} PM")

        # Atualiza o time_edit para manter compatibilidade
        self.time_edit.setTime(time)

    def validate_time_input(self, text):
        """
        Valida e corrige o texto de entrada em tempo real.
        Verifica apenas o formato AM/PM, permitindo edição livre do resto.
        """
        if not text:
            return

        # Se contém espaço, verifica o que vem depois
        if " " in text:
            parts = text.split(" ", 1)
            time_part = parts[0]
            ampm_part = parts[1].upper()

            # Verifica se o AM/PM está correto, mas apenas se tiver 2 caracteres ou mais
            if len(ampm_part) >= 2:
                # Se começa com A, deve ser AM
                if ampm_part.startswith("A") and ampm_part != "AM":
                    ampm_part = "AM"
                # Se começa com P, deve ser PM
                elif ampm_part.startswith("P") and ampm_part != "PM":
                    ampm_part = "PM"
                # Se não é AM nem PM e tem 2+ caracteres, verifica se é inválido
                elif ampm_part != "AM" and ampm_part != "PM" and not (ampm_part == "A" or ampm_part == "P"):
                    # Se não começa com A ou P, remove completamente
                    ampm_part = ""

                # Só corrige se for diferente e tiver 2+ caracteres
                if ampm_part != parts[1].upper() and len(parts[1]) >= 2:
                    # Atualiza o texto no combobox
                    corrected_text = time_part
                    if ampm_part:
                        corrected_text += f" {ampm_part}"

                    if corrected_text != text:
                        # Desconecta temporariamente para evitar recursão
                        line_edit = self.time_combo.lineEdit()
                        if line_edit:
                            line_edit.textEdited.disconnect(self.validate_time_input)
                            self.time_combo.setCurrentText(corrected_text)
                            # Mantém a posição do cursor
                            cursor_pos = line_edit.cursorPosition()
                            line_edit.setCursorPosition(min(cursor_pos, len(corrected_text)))
                            line_edit.textEdited.connect(self.validate_time_input)

    def format_time_on_focus_out(self):
        """
        Formata o texto do combobox quando o usuário termina de editar (perde o foco).
        Garante que o formato final esteja correto.
        """
        # Obtém o texto atual
        text = self.time_combo.currentText()

        # Se o texto estiver vazio, usa um valor padrão
        if not text.strip():
            if self.language == "English":
                text = "12:00 PM"  # Meio-dia como padrão
            else:
                text = "12:00"     # Meio-dia como padrão

        # Processa o texto para extrair a hora e minuto
        try:
            # Tenta extrair a hora do texto atual
            if self.language == "English":
                # Verifica se contém AM/PM
                is_am = "am" in text.lower()
                is_pm = "pm" in text.lower()

                # Remove AM/PM para processamento
                clean_text = text.lower().replace("am", "").replace("pm", "").strip()

                # Extrai hora e minuto
                if ":" in clean_text:
                    parts = clean_text.split(":")
                    hour = int(parts[0]) if parts[0].strip() else 12
                    minute = int(parts[1]) if len(parts) > 1 and parts[1].strip() else 0
                else:
                    # Apenas hora
                    hour = int(clean_text) if clean_text.strip() else 12
                    minute = 0

                # Ajusta a hora com base em AM/PM
                if is_pm and hour < 12:
                    hour += 12
                elif is_am and hour == 12:
                    hour = 0
                elif not is_am and not is_pm:
                    # Se não tem AM/PM, assume com base na hora
                    if hour < 12:
                        is_am = True
                    else:
                        is_pm = True
                        if hour > 12:
                            hour -= 12
            else:
                # Formato 24 horas para português
                if ":" in text:
                    parts = text.split(":")
                    hour = int(parts[0]) if parts[0].strip() else 0
                    minute = int(parts[1]) if len(parts) > 1 and parts[1].strip() else 0
                else:
                    # Apenas hora
                    hour = int(text) if text.strip() else 0
                    minute = 0
        except ValueError:
            # Se falhar ao extrair a hora, usa o valor atual do QTimeEdit
            time = self.time_edit.time()
            hour = time.hour()
            minute = time.minute()

            if self.language == "English":
                # Determina AM/PM
                is_am = hour < 12
                is_pm = hour >= 12
                if hour > 12:
                    hour -= 12
                elif hour == 0:
                    hour = 12

        # Formata de acordo com o idioma
        if self.language == "English":
            # Determina se é AM ou PM para o formato final
            if is_pm:
                am_pm = "PM"
                display_hour = hour if hour <= 12 else hour - 12
                if display_hour == 0:
                    display_hour = 12
            else:
                am_pm = "AM"
                display_hour = hour if hour > 0 else 12

            # Formata para o padrão "01:00 AM"
            formatted_text = f"{display_hour:02d}:{minute:02d} {am_pm}"
        else:
            # Formato 24 horas para português
            formatted_text = f"{hour:02d}:{minute:02d}"

        # Atualiza o texto e o QTimeEdit
        if text != formatted_text:
            # Desconecta temporariamente os sinais para evitar recursão
            self.time_combo.currentTextChanged.disconnect(self.update_time_edit)
            self.time_combo.setCurrentText(formatted_text)
            self.time_combo.currentTextChanged.connect(self.update_time_edit)

            # Atualiza o QTimeEdit para manter consistência
            if self.language == "English":
                # Converte para formato 24 horas para o QTimeEdit
                hour_24 = hour
                if am_pm == "PM" and hour < 12:
                    hour_24 += 12
                elif am_pm == "AM" and hour == 12:
                    hour_24 = 0
                self.time_edit.setTime(QTime(hour_24, minute))
            else:
                self.time_edit.setTime(QTime(hour, minute))

    def update_language(self, language):
        """
        Atualiza o idioma do widget (dropdown).

        Args:
            language (str): Idioma a ser definido.
        """
        self.language = language
        if self.language != "English":
            self.language = "Português"
            self.time_combo.setFixedWidth(70)
        else:
            self.time_combo.setFixedWidth(90)
        self.populate_time_options()
        # Atualiza o validador para o novo idioma
        self.setup_validator()

        # Reconecta os eventos
        line_edit = self.time_combo.lineEdit()
        if line_edit:
            # Desconecta eventos existentes
            try:
                line_edit.editingFinished.disconnect()
            except:
                pass

            try:
                line_edit.textEdited.disconnect()
            except:
                pass

            # Reconecta os eventos
            line_edit.editingFinished.connect(self.format_time_on_focus_out)

            # Conecta o evento de edição apenas para o inglês
            if self.language == "English":
                line_edit.textEdited.connect(self.validate_time_input)

        # Atualiza o valor exibido no dropdown
        self.setTime(self.time_edit.time())

    def try_delete(self):
        """
        Caso exista, remove o widget de horário.
        """
        if self.parent() and self.parent().layout():
            dialog = self.get_scheduler_dialog()
            if dialog:
                dialog.remove_time_input(self)

    def get_scheduler_dialog(self):
        """
        Auxilia encontrando o SchedulerDialog (parent).

        Returns:
            SchedulerDialog: O diálogo pai, ou None se não encontrado.
        """
        parent = self.parent()
        while parent:
            if hasattr(parent, 'remove_time_input'):
                return parent
            parent = parent.parent()
        return None
