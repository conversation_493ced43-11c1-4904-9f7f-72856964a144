[Read in English](#english-version) | [Ler em Português](#versao-em-portugues)

---

<a name="versao-em-portugues"></a>

# Pratiko (Versão em Português)

Repositório para o software **Pratiko** desenvolvido pela **ES Logistics**. Este repositório é destinado apenas aos colaboradores da empresa.

## Pull Requests

Contribuições são bem-vindas! Se você encontrar bugs ou quiser adicionar funcionalidades, sinta-se à vontade para fazer pull requests, mas lembre-se de que este repositório é exclusivo para uso interno.

## Passos para mudanças/contribuições

1. **Fork** o repositório.
2. **Clone** o repositório para a sua máquina local.
3. Crie uma nova **branch** para suas alterações (ex: `git checkout -b feature/sua-feature` ou `bugfix/seu-bugfix`).
4. Faça suas alterações e **commit** com uma mensagem clara.
5. **Push** para a sua branch (ex: `git push origin feature/sua-feature`).
6. Abra um **pull request** para revisão e aprovação.

## Reporting Bugs e Issues

Se você encontrar algum bug ou problema, siga os passos abaixo para relatar:

1. Vá para [Issues](https://github.com/AutomacaoBI/Pratiko/issues) no GitHub.
2. Verifique se o issue já não existe.
3. Crie um novo **issue** detalhando o problema encontrado.
4. Inclua informações como passos para reproduzir o erro, sistema operacional, versão do software, e qualquer outra informação relevante (screenshots são úteis).

---

### 📌 Histórico de Versões

| Data        | Versão        | Descrição                                                                 |
|-------------|---------------|---------------------------------------------------------------------------|
| 12/03/2025  | PRATIKO_V1    | 1ª versão                                                                 |
| 17/03/2025  | PRATIKO_V2    | Alteração no layout                                                       |
| 14/04/2025  | PRATIKO_V2.2  | Retirada da necessidade de API KEY durante a instalação; inclusão do campo "Adicionar Chave"; suporte a múltiplas chaves; melhorias no layout |
| 16/04/2025  | PRATIKO_V2.3  | Retirada da necessidade de ADMIN; abertura em tela cheia                 |
| 28/04/2025  | PRATIKO_V2.4  | Seletor de idioma (PT/EN); seletor de colunas; idioma detectado automaticamente; dados de Transbordo e Containers; ocultação de colunas sem dados |
| 26/05/2025  | PRATIKO_V2.5  | **(Lançamento previsto)** Busca por status de processo; agendador interno; execução em segundo plano |

---

## Licença
Este repositório é de uso interno e não está licenciado para uso público. Todos os direitos reservados à **ES Logistics**.

---

**Nota:** Certifique-se de que qualquer modificação ou contribuição siga as diretrizes internas da empresa.

---

---

<a name="english-version"></a>

# Pratiko (English Version)

Repository for the **Pratiko** software developed by **ES Logistics**. This repository is intended only for company employees.

## Pull Requests

Contributions are welcome! If you find bugs or want to add features, feel free to submit pull requests, but remember this repository is exclusively for internal use.

## Steps for Changes/Contributions

1. **Fork** the repository.
2. **Clone** the repository to your local machine.
3. Create a new **branch** for your changes (e.g., `git checkout -b feature/your-feature` or `bugfix/your-bugfix`).
4. Make your changes and **commit** with a clear message.
5. **Push** to your branch (e.g., `git push origin feature/your-feature`).
6. Open a **pull request** for review and approval.

## Reporting Bugs and Issues

If you find any bug or issue, follow the steps below to report it:

1. Go to [Issues](https://github.com/AutomacaoBI/Pratiko/issues) on GitHub.
2. Check if the issue already exists.
3. Create a new **issue** detailing the problem found.
4. Include information such as steps to reproduce the error, operating system, software version, and any other relevant information (screenshots are helpful).

---

### 📌 Version History

| Date        | Version       | Description                                                               |
|-------------|---------------|---------------------------------------------------------------------------|
| 2025-03-12  | PRATIKO_V1    | First version                                                             |
| 2025-03-17  | PRATIKO_V2    | Layout changes                                                            |
| 2025-04-14  | PRATIKO_V2.2  | Removed API KEY requirement during install; added "Add Key" directly in the UI; support for multiple keys; layout improvements |
| 2025-04-16  | PRATIKO_V2.3  | Removed ADMIN requirement; full screen on launch                         |
| 2025-04-28  | PRATIKO_V2.4  | Language selector (PT/EN); column selector; automatic language detection; added Transshipment and Containers data; auto-hide empty columns |
| 2025-05-26  | PRATIKO_V2.5  | **(Planned release)** Search by process status; internal export scheduler; background operation |

---

## License

This repository is for internal use only and is not licensed for public use. All rights reserved to **ES Logistics**.

---

**Note:** Ensure that any modification or contribution follows the company's internal guidelines.
