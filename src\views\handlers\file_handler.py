import os
import json
from pathlib import Path

import pandas as pd

from views.utils.front_services import (
    exibir_question,
    exibir_erro, 
    exibir_information
)
from services.language_utils.translations_functions import translate as t

from PySide6.QtWidgets import (
    QFileDialog, 
    QInputDialog, 
    QMessageBox,
    QApplication,
)


def salvar_excel(df, parent):
    """
    Salva o DataFrame em um arquivo Excel com o nome e caminho especificados pelo usuário.
    
    Args:
        df (pd.DataFrame): DataFrame a ser salvo.
        parent (QWidget): <PERSON>la pai para exibir mensagens de erro.
    """
    if df is None:
        exibir_erro(parent, t("Nenhum dado para salvar."))
        return

    filename, _ = QFileDialog.getSaveFileName(
        parent = parent, 
        caption = t("Salvar Arquivo", parent.current_language), 
        dir = "", 
        filter = "Excel Files (*.xlsx)"
    )
    if not filename:
        return
    
    try:
        columns_to_save = getattr(parent, 'selected_columns', df.columns)
        valid_columns = [col for col in columns_to_save if col in df.columns]
        
        if not valid_columns:
            exibir_erro(parent, t("Nenhuma das colunas selecionadas existe nos dados."))
            return
            
        # Translate as colunas SE o idioma estiver em inglês
        lang = getattr(parent, "current_language", "Português")
        df_to_save = df[valid_columns].copy()
        if lang == "English":
            translated_columns = [t(col) for col in valid_columns]
            df_to_save.columns = translated_columns

        with pd.ExcelWriter(filename, engine="openpyxl") as writer:
            df_to_save.to_excel(writer, sheet_name="FollowUp", index=False)
            
        exibir_information(
            parent, t("Arquivo Excel salvo em {filename}").format(filename=filename)
        )
    except Exception as e:
        exibir_erro(
            parent, t("Erro ao salvar o arquivo Excel: {error}").format(error=str(e))
        )


class FileHandler:
    """
    Classe para manipulação de arquivos, incluindo leitura e escrita de chaves de API.
    """

    @staticmethod
    def incluir_api_keys(api_key_path, parent):
        """
        Adiciona novas chaves de API ao arquivo especificado.
        
        Args:
            api_key_path (str): Caminho do arquivo de chaves de API.
            parent (QWidget): Janela pai para exibir mensagens de erro.
            
        Returns:
            dict: Dicionário com as chaves de API adicionadas, ou None se não houver chaves.
        """
        api_key_dir = Path(api_key_path).parent
        api_key_dir.mkdir(parents=True, exist_ok=True)

        # Dando load nas API_Keys existentes
        if os.path.exists(api_key_path):
            with open(api_key_path, "r") as file:
                api_keys = json.load(file)
        else:
            api_keys = {}

        keys_added = False

        while True:
            # Dialog para inserir o nome da empresa
            name_dialog = QInputDialog(parent)
            name_dialog.setWindowTitle(t("Incluir API Key", parent.current_language))
            name_dialog.setLabelText(
                t("Insira o <b>NOME DA EMPRESA</b>:", parent.current_language)
            )
            name_dialog.setWindowIcon(QApplication.instance().windowIcon())

            if not name_dialog.exec():
                break

            name = name_dialog.textValue()
            if not name.strip():
                break

            elif name.strip() in api_keys:
                exibir_erro(
                    parent, 
                    t("Nome já existe. Insira um <b>nome</b> diferente.", parent.current_language)
                )
                continue

            # Dialog para inserir a chave de API
            key_dialog = QInputDialog(parent)
            key_dialog.setWindowTitle(t("Incluir chave", parent.current_language))
            key_dialog.setLabelText(
                t("Insira a nova <b>CHAVE DE API</b>:", parent.current_language)
            )
            key_dialog.setWindowIcon(QApplication.instance().windowIcon())

            if not key_dialog.exec():
                break
            
            api_key = key_dialog.textValue()
            if not api_key.strip():
                break
                
            elif api_key.strip() in api_keys.values():
                exibir_erro(
                    parent, 
                    t("API Key já existe. Insira uma <b>chave diferente</b>.", parent.current_language)
                )
                continue

            if len(api_key) < 32:
                exibir_erro(
                    parent, 
                    t("<b>A API Key deve ter pelo menos 32 caracteres</b>.", parent.current_language)
                )
                continue

            api_keys[name.strip()] = api_key.strip()
            keys_added = True

            add_more = exibir_question(
                parent, 
                t("Deseja adicionar outra chave de API?", parent.current_language))

            if add_more != QMessageBox.Yes:
                break

        if keys_added:
            try:
                with open(api_key_path, "w") as file:
                    json.dump(api_keys, file, indent=4)
                    exibir_information(
                        parent, 
                        t("Chave(s) adicionada(s) com <b>sucesso</b>.", parent.current_language)
                    )
                    return api_keys
            except Exception:
                exibir_erro(
                    parent, 
                    t("<b>Erro</b> ao salvar chave de API.", parent.current_language)
                )
                return None
    
    @staticmethod
    def excluir_api_key_selecionada(api_key_path, key_to_delete, parent):
        """
        Exclui uma chave de API do arquivo especificado.
        
        Args:
            api_key_path (str): Caminho do arquivo de chaves de API.
            key_to_delete (str): Nome da chave de API a ser excluída.
            parent (QWidget): Janela pai para exibir mensagens de erro.
            
        Returns:
            dict: Dicionário com as chaves de API restantes, ou None se não houver chaves.
        """
        if not os.path.exists(api_key_path):
            exibir_erro(
                parent, 
                t("Arquivo de chaves de API não encontrado.", parent.current_language)
            )
            return None

        try:
            with open(api_key_path, "r") as file:
                api_keys = json.load(file)
        except json.JSONDecodeError:
            exibir_erro(
                parent, 
                t("Arquivo de chaves de API inválido.", parent.current_language)
            )
            return None
        except Exception:
            exibir_erro(
                parent, 
                t(f"Erro ao ler o arquivo de chaves de API.", parent.current_language)
            )
            return None

        if key_to_delete in api_keys:
            del api_keys[key_to_delete]

            try:
                with open(api_key_path, "w") as file:
                    json.dump(api_keys, file, indent=4)
                    exibir_information(
                        parent, 
                        t("Chave de API excluída com sucesso.", parent.current_language)
                    )

                    # Limpa a tabela caso todas as chaves sejam excluídas
                    if len(api_keys) == 0:
                        parent.tabela.setRowCount(0)
                        parent.tabela.setColumnCount(0)
                    return api_keys
            except Exception:
                exibir_erro(
                    parent, 
                    t(f"Erro ao salvar o arquivo de chaves de API.", parent.current_language)
                )
                return None
        else:
            exibir_erro(
                parent, 
                t("Chave de API nao encontrada.", parent.current_language)
            )
            return None
