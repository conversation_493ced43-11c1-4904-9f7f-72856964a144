import os
import json

import pandas as pd
from dataclasses import dataclass
from typing import Optional, Union
from argparse import Namespace

from services.api_client import APIClient
from services.utils.validator import DataValidator
from services.handlers.cli_file_handler import cli_save_data
from services.language_utils.translations_functions import translate as t


@dataclass
class ExportConfig:
    """
    Configuração para exportação de dados
    Serve para padronizar a entrada de dados tanto do CLI quanto do Scheduler.

    Attributes:
        dt_inicial (str): Data inicial no formato 'dd/mm/aaaa' ou 'yyyy-mm-dd'
        dt_final (str): Data final no formato 'dd/mm/aaaa' ou 'yyyy-mm-dd'
        output_dir (str): Diretório para salvar o arquivo
        output_format (str): Formato para salvar ('csv' ou 'xlsx')
        api_key (str, optional): API key para autenticação
        company_name (str, optional): Nome da empresa para incluir no nome do arquivo

    """

    dt_inicial: str
    dt_final: str
    output_dir: str = os.path.join(os.path.expanduser('~'), 'Downloads')
    output_format: str = 'xlsx'
    api_key: Optional[str] = None
    company_name: Optional[str] = None

class CLIController:
    """
    Controlador para exportação de dados via CLI ou Scheduler.
    """

    def __init__(self, api_key=None, config_path=None):
        self.api_key = api_key

        # Se o config_path não for fornecido, tenta encontrar o arquivo keys.json
        if not config_path:
            try:
                import sys

                # Lista de possíveis locais para o arquivo keys.json
                possible_locations = []

                # 1. No diretório do executável (quando empacotado)
                if hasattr(sys, "_MEIPASS"):
                    possible_locations.append(os.path.join(os.path.dirname(sys.executable), 'keys.json'))

                # 2. No diretório raiz do projeto
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                possible_locations.append(os.path.join(project_root, 'keys.json'))

                # 3. No diretório src
                possible_locations.append(os.path.join(project_root, 'src', 'keys.json'))

                # 4. No mesmo diretório que o módulo
                possible_locations.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'keys.json'))

                # Verifica cada local e usa o primeiro que existir
                for location in possible_locations:
                    if os.path.exists(location):
                        self.config_path = location
                        break
                else:
                    # Se nenhum for encontrado, usa o padrão no diretório src
                    self.config_path = os.path.join(project_root, 'src', 'keys.json')

            except Exception:
                # Fallback para o diretório src
                self.config_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    'src',
                    'keys.json'
                )
        else:
            # Caso o caminho seja fornecido, usa o fornecido
            self.config_path = config_path


        # Inicializa validador de dados 
        self._validator = DataValidator()

    def export_data(self, config: Union[Namespace, ExportConfig, dict]) -> bool:
        """
        Exporta dados baseado na configuração fornecida.

        Args:
            config: Pode ser um dos:
                - argparse.Namespace do CLI
                - ExportConfig do agendador
                - dicionário com campos obrigatórios

        Returns:
            bool: True se exportação bem sucedida, False caso contrário
        """
        # Converte entrada para ExportConfig
        if isinstance(config, dict):
            config = ExportConfig(**config)

        elif isinstance(config, Namespace):
            config = ExportConfig(
                dt_inicial=config.dt_inicial,
                dt_final=config.dt_final,
                output_dir=getattr(
                    config,
                    'output_dir',
                    os.path.join(os.path.expanduser('~'), 'Downloads')
                ),
                output_format=getattr(config, 'output_format', 'xlsx'),
                api_key=getattr(config, 'api_key', None)
            )

        # Valida as datas de entrada
        if not (self._validator.validar_data_input(config.dt_inicial) and
                self._validator.validar_data_input(config.dt_final)):
            print(t("Erro: Formato de data inválido. Use dd/mm/aaaa ou yyyy-mm-dd"))
            return False

        # Obtém API key com um fallback
        api_key = config.api_key or self.api_key or self._load_api_key_from_config()

        if not api_key:
            print(t("Erro: API key não fornecida e não encontrada no arquivo de configuração"))
            return False

        # Inicializa cliente API e obtém dados
        api_client = APIClient(api_key=api_key)
        response = api_client.obter_dados_processo(
            dt_inicial=config.dt_inicial,
            dt_final=config.dt_final
        )

        # Verifica se a resposta é None
        if response is None:
            # Cria um DataFrame vazio com colunas padrão para evitar erros
            df = pd.DataFrame(columns=[
                "Ref. cliente", "Ref. Sistema", "Master", "House", "Navio", "Viagem",
                "Carrier", "Porto de Origem", "Porto de Destino", "Incoterm",
                "Consignee", "Notify", "Shipper", "Containers", "Transbordo"
            ])
            # Adiciona uma linha vazia para garantir que o arquivo seja criado
            df.loc[0] = [""] * len(df.columns)
            print(t("Aviso: Nenhum dado encontrado para o período especificado."))
        elif isinstance(response, dict):
            if 'dados' in response:
                # Verifica se 'dados' é uma lista ou dicionário
                if isinstance(response['dados'], list) or isinstance(response['dados'], dict):
                    df = pd.DataFrame(
                        response['dados'] 
                        if isinstance(response['dados'], list) else [response['dados']]
                    )
                else:
                    print(t("Erro: O campo 'dados' da API não é uma lista ou dicionário"))
                    return False
            else:
                print(t("Erro: Resposta da API não contém o campo 'dados'"))
                return False
        elif isinstance(response, pd.DataFrame):
            df = response
        else:
            print(
                t(
                    "Erro: Formato de resposta inválido da API. Tipo: {tipo}"
                    ).format(tipo=type(response))
                )
            return False

        # Filtra as colunas
        selected_columns = self.load_selected_columns()

        if selected_columns and len(df.columns) > 0:
            valid_columns = [col for col in selected_columns if col in df.columns]

            if valid_columns:
                df = df[valid_columns]

        # Filtra colunas vazias
        filter_empty_columns = getattr(config, 'filter_empty_columns', False)

        if filter_empty_columns and df is not None and len(df.columns) > 0:
            non_empty_columns = []
            
            for col in df.columns:
                try:
                    if col in ["Containers", "Transbordo"]:
                        has_non_empty = False

                        for val in df[col]:
                            # Verifica se o valor é um array ou lista
                            if isinstance(val, (list, tuple, pd.Series, pd.DataFrame)):
                                # Se for uma lista, verifica se tem algum elemento
                                if len(val) > 0:
                                    has_non_empty = True

                            # Verificar se o pandas
                            elif pd.notna(val) and (not isinstance(val, str) or val.strip() != ""):
                                has_non_empty = True

                        if has_non_empty:
                            non_empty_columns.append(col)
                    if col not in ["Containers", "Transbordo"]:
                        # Verifica se a coluna tem pelo menos um valor não vazio
                        def check_non_empty(val):
                            if pd.isna(val):
                                return False
                            if isinstance(val, str):
                                return val.strip() != ""
                            # Para outros tipos (listas, dicionários, etc.), considera não vazio
                            return True

                        non_empty_values = df[col].apply(check_non_empty)

                        if non_empty_values.any():
                            non_empty_columns.append(col)

                except Exception:
                    # Se ocorrer um erro, mantém a coluna sem exibir mensagem
                    non_empty_columns.append(col)

            if non_empty_columns:
                df = df[non_empty_columns]

        # Salva os dados
        if df is not None:
            filepath = cli_save_data(
                df,
                config.output_dir,
                file_format=config.output_format,
                company_name=getattr(config, 'company_name', None)
            )

            if filepath:
                print(
                    t("Arquivo salvo com sucesso em: {filepath}").format(filepath=filepath)
                )
                return True

        print(t("Erro: Não foi possível obter ou salvar os dados"))
        return False

    def load_selected_columns(self):
        """
        Carrega as colunas selecionadas do arquivo de configuração
        """
        try:
            config_dir = os.path.dirname(self.config_path)
            columns_path = os.path.join(config_dir, 'columns.json')

            if os.path.exists(columns_path):
                with open(columns_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('selected_columns', None)
            return None
        except Exception:
            return None

    def _load_api_key_from_config(self):
        """
        Carrega primeira API key do arquivo de configuração
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    keys = json.load(f)

                    # Retorna a primeira chave encontrada
                    return next(iter(keys.values())) if keys else None
            return None
        except Exception:
            print(t("Erro ao carregar API key do arquivo de configuração."))
            return None
