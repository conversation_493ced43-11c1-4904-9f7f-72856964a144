import sys
from pathlib import Path
import pandas as pd

from views.utils.date_utils import dias_entre_datas
from services.language_utils.translations_functions import translate as t
from views.utils.front_services import (
    exibir_erro,
    exibir_information
)

from views.handlers.status_handler import StatusHandler

class ConsultaHandler:
    """
    Classe responsável por gerenciar a consulta de dados do processo.

    """
    def __init__(self, main_controller, form_inputs, tabela, btn_salvar, parent):
        """
        Inicializa o handler de consulta.

        Args:
            main_controller (MainController): Controlador principal para obter os dados.
            form_inputs (FormInputs): Widget com os campos de entrada.
            tabela (DataTable): Widget da tabela para exibir os dados.
            btn_salvar (QPushButton): Botão para salvar os dados.
            parent (QWidget): <PERSON>la pai para exibir mensagens de erro.
        """
        self.main_controller = main_controller
        self.form_inputs = form_inputs
        self.tabela = tabela
        self.btn_salvar = btn_salvar
        self.parent = parent

        # Passa a referência do parent para o controller para atualizar o status
        if hasattr(self.main_controller, 'parent'):
            self.main_controller.parent = self.parent

    def _validar_campos(self) -> bool:
        """
        Valida os campos do formulário.
        """
        self.ds_processo = self.form_inputs.entry_processo.text()
        self.dt_inicial = self.form_inputs.entry_dt_inicial.text()
        self.dt_final = self.form_inputs.entry_dt_final.text()
        self.ref_cliente = self.form_inputs.entry_ref_cliente.text()
        self.nr_container = self.form_inputs.entry_nr_container.text()

        if not any([self.ds_processo, self.dt_inicial, self.dt_final,
                   self.ref_cliente, self.nr_container]):
            exibir_erro(self.parent, t("Preencha pelo menos um dos campos obrigatórios."))
            return False
        return True

    def _validar_datas(self) -> bool:
        """
        Valida as datas inseridas.
        """
        if self.dt_final and self.dt_inicial:
            try:
                days_diff = dias_entre_datas(self.dt_inicial, self.dt_final)
            except Exception:
                exibir_erro(self.parent, t("Data inicial inválida! Use o formato dd/mm/aaaa."))
                return False

            if days_diff < 0:
                exibir_information(self.parent, t("Data inicial deve ser menor que a data final."))
                return False

            if days_diff > 30:
                exibir_information(self.parent, t("O intervalo de datas deve ser menor que 30 dias."))
                return False
        return True

    def _processar_dados(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Processa os dados obtidos aplicando filtros necessários.
        """
        if df is not None and not df.empty:
            selected_filter = getattr(self.parent, "selected_filter", None)
            if selected_filter:
                return StatusHandler.filtrar_processos_por_status(
                    df,
                    selected_filter,
                    t,
                    self.parent.current_language,
                    self.parent
                )
            return df
        return None

    def executar_consulta(self) -> pd.DataFrame:
        """
        Executa a consulta com os dados fornecidos pelo usuário.
        """
        if not self._validar_campos():
            return None

        if not self._validar_datas():
            return None

        if not self.main_controller:
            exibir_erro(self.parent, t("Selecione uma chave de API antes de consultar."))
            return None

        try:

            df = self.main_controller.obter_dados_processo(
                self.ds_processo, 
                self.dt_inicial, 
                self.dt_final,
                self.ref_cliente, 
                self.nr_container
            )

            filtered_df = self._processar_dados(df)

            if filtered_df is not None and not filtered_df.empty:
                self.btn_salvar.setEnabled(True)
                self.tabela.display_data(filtered_df)
                return filtered_df

            self.btn_salvar.setEnabled(False)
            self.tabela.setRowCount(0)
            self.tabela.setColumnCount(0)
            return None

        except Exception:
            exibir_erro(self.parent, t("Erro ao buscar dados, tente novamente."))
            self.btn_salvar.setEnabled(False)
            self.tabela.setRowCount(0)
            self.tabela.setColumnCount(0)

    @staticmethod
    def resolve_path(relative_path):
        """
        Resolve o caminho relativo para o diretório base do aplicativo.
        """
        try:
            base_path = Path(sys._MEIPASS)
        except AttributeError:
            base_path = Path(__file__).parent
        return str(base_path / relative_path)
