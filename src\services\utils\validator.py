import re


class DataValidator:
    """
    Classe responsável por validar os dados de entrada do usuário.
    """

    @staticmethod
    def validar_data_input(data):
        """
        Valida se a data está no formato correto.
        """
        return (
            bool(re.match(r"\d{2}/\d{2}/\d{4}", data))
            or bool(re.match(r"\d{4}-\d{2}-\d{2}", data))
            or data == ""
        )
