import pandas as pd

from views.utils.front_services import exibir_information

class StatusHandler:
    """
    Classe para manipulação de status de processos.
    """

    @staticmethod
    def filtrar_processos_por_status(df, status, t, current_language, parent=None) -> pd.DataFrame:
        """
        Filtra os processos com base no status fornecido.

        Args:
            df (pd.DataFrame): DataFrame contendo os dados dos processos.
            status (str): Status a ser filtrado. Pode ser "Todos os Processos", "Processos em Andamento" ou "Processos Finalizados".
            t (function): Função de tradução.
            current_language (str): Idioma atual.
            parent (QWidget, optional): <PERSON>la pai para exibir mensagens de erro. Padrão é None.
        """

        if df is None or df.empty:
            return df

        col_embarque = "Confirmação de embarque"
        col_chegada = "Confirmação de chegada"

        # Verifica se as colunas existem no DataFrame
        if col_embarque not in df.columns or col_chegada not in df.columns:
            return df

        todos = t("Todos os Processos", current_language)
        andamento = t("Processos em Andamento", current_language)
        finalizado = t("Processos Finalizados", current_language)

        def has_date(series):
            """
            Verifica se a série tem valores de data válidos.
            Retorna uma série booleana onde True indica que o valor é uma data válida.
            """
            # Cria uma máscara booleana para cada condição
            mask1 = pd.notna(series)
            mask2 = (series != "-")
            # Converte para string apenas se não for None
            mask3 = series.fillna("none").astype(str).str.lower().ne("none")

            # Combina as máscaras com operador &
            return mask1 & mask2 & mask3

        if status in [None, todos]:
            return df

        elif status == andamento:
            mask = ~has_date(df[col_chegada])
            filtered_df = df[mask].reset_index(drop=True)

            if parent:
                if len(filtered_df) == 0:
                    exibir_information(parent, t("Nenhum processo em andamento encontrado."))
                else:
                    exibir_information(
                        parent,
                        t("Foram encontrados {count} processos em andamento.",
                          current_language).format(count=len(filtered_df))
                    )

            return filtered_df

        elif status == finalizado:
            # Finalizado: Confirmação de embarque e Confirmação de chegada preenchidos
            mask = has_date(df[col_chegada])
            filtered_df = df[mask].reset_index(drop=True)

            if parent:
                if len(filtered_df) == 0:
                    exibir_information(
                        parent,
                        t("Nenhum processo finalizado encontrado.",
                          current_language)
                    )
                else:
                    exibir_information(
                        parent,
                        t("Foram encontrados {count} processos finalizados.",
                          current_language).format(count=len(filtered_df))
                    )
            return filtered_df

        return df
