from PySide6.QtCore import QPropertyAnimation, QEasingCurve, QPoint, QRect
from PySide6.QtWidgets import QDialog, QWidget

class WindowAnimations:
    """
    Classe para gerenciar animações de janelas.
    """

    @staticmethod
    def setup_dialog_animations(dialog: QDialog):
        """
        Anima a janela de uma posição inicial para uma posição final.
        """
        # Animação de fade in
        fade_animation = QPropertyAnimation(dialog, b"windowOpacity")
        fade_animation.setDuration(250)
        fade_animation.setStartValue(0.0)
        fade_animation.setEndValue(1.0)
        fade_animation.setEasingCurve(QEasingCurve.InOutQuad)

        slide_animation = QPropertyAnimation(dialog, b"geometry")
        slide_animation.setDuration(250)
        slide_animation.setEasingCurve(QEasingCurve.InOutQuad)

        return fade_animation, slide_animation

    @staticmethod
    def setup_window_animations(window: QWidget):
        """
        Configura animações para a janela principal.

        Args:
            window (QWidget): A janela principal a ser animada.

        Returns:
            tuple: Animações de slide in e fade in.
        """
        # Animação de fade in
        fade_in_animation = QPropertyAnimation(window, b"windowOpacity")
        fade_in_animation.setDuration(250)
        fade_in_animation.setStartValue(0.0)
        fade_in_animation.setEndValue(1.0)
        fade_in_animation.setEasingCurve(QEasingCurve.InOutQuad)

        # animação de scale
        scale_animation = QPropertyAnimation(window, b"geometry")
        scale_animation.setDuration(250)
        scale_animation.setEasingCurve(QEasingCurve.InOutQuad)

        return scale_animation, fade_in_animation

    @staticmethod
    def animate_dialog_open(dialog: QDialog):
        """
        Anima a abertura de um diálogo.
        """
        fade_animation, slide_animation = WindowAnimations.setup_dialog_animations(dialog)

        if dialog.parent():
            parent_center = dialog.parent().rect().center()
        if not dialog.parent():
            parent_center = dialog.screen().availableGeometry().center()

        start_position = QPoint(parent_center.x() - dialog.width() / 2,
                                parent_center.y() - dialog.height())
        end_position = QPoint(parent_center.x() - dialog.width() / 2,
                              parent_center.y() - dialog.height())

        slide_animation.setStartValue(start_position)
        slide_animation.setEndValue(end_position)

        # Inicia as animações
        fade_animation.start()
        slide_animation.start()

    @staticmethod
    def animate_window_open(window: QWidget):
        """
        Anima a abertura da janela principal.
        """
        window.show()
        window.setWindowOpacity(0.0)

        # Apenas animação de fade in
        fade_in_animation = QPropertyAnimation(window, b"windowOpacity")
        fade_in_animation.setDuration(350)
        fade_in_animation.setStartValue(0.0)
        fade_in_animation.setEndValue(1.0)
        fade_in_animation.setEasingCurve(QEasingCurve.InOutQuad)

        # Armazena a animação como atributo para evitar coleta de lixo
        window._fade_animation = fade_in_animation

        # Inicia a animação
        fade_in_animation.start()
