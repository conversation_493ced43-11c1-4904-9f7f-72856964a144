from services.utils.validator import DataValidator
from services.api_client import APIClient
from services.data_transformer import DataTransformer
from views.data_table import DataTable
from views.utils.front_services import exibir_erro
from PySide6.QtWidgets import <PERSON><PERSON>abel, QPushButton
from services.language_utils.translations_functions import translate as t
from views.utils.front_services import exibir_information


class MainController:
    """
    Controlador principal da interface gráfica
    """

    def __init__(self, api_key, parent=None):
        self.api_key = api_key
        self.api_client = APIClient(api_key)
        self.data_transformer = DataTransformer()
        self.data_validator = DataValidator()
        self.label_status = QLabel()
        self.btn_salvar = QPushButton()
        self.df = None
        self.parent = parent

    def exibir_dados_na_tabela(self):
        """
        Exibe os dados obtidos na tabela
        """
        self.data_table = DataTable()
        self.data_table.display_data(self.df)

    def obter_dados_processo(
            self,
            ds_processo=None,
            dt_inicial=None,
            dt_final=None,
            ref_cliente=None,
            nr_container=None,
    ):
        """
        Valida os dados do processo e chama a API para obter os dados
        """
        if dt_inicial and not self.data_validator.validar_data_input(dt_inicial):
            exibir_erro(
                self.label_status,
                t("Data inicial inválida.", self.current_language)
            )
            return
        if dt_final and not self.data_validator.validar_data_input(dt_final):
            exibir_erro(
                self.label_status,
                t("Data final inválida.", self.current_language)
            )
            return

        # Converte as datas para o formato correto
        converted_dt_inicial = None

        if dt_inicial:
            try:
                dia, mes, ano = dt_inicial.split("/")
                converted_dt_inicial = f"{ano}-{mes}-{dia}"
            except ValueError:
                exibir_erro(
                    self.label_status,
                    t("Data inicial inválida! Use o formato dd/mm/aaaa.", self.current_language)
                )
                return

        converted_dt_final = None

        if dt_final:
            try:
                dia, mes, ano = dt_final.split("/")
                converted_dt_final = f"{ano}-{mes}-{dia}"
            except ValueError:
                exibir_erro(
                    self.label_status,
                    t("Data final inválida! Use o formato dd/mm/aaaa.", self.current_language)
                )
                return

        # Verifica se os dados são válidos
        try:
            dados = self.api_client.obter_dados_processo(
                ds_processo, converted_dt_inicial, converted_dt_final, ref_cliente, nr_container
            )

            # Se os dados forem obtidos, transforma e exibe na tabela
            if dados:
                self.df = self.data_transformer.json_para_dataframe(dados)

                if self.df is not None:
                    # Verifica se o parent tem os atributos necessários para armazenar a chave de status
                    if hasattr(self, 'parent') and hasattr(self.parent, 'current_status_key'):
                        self.parent.current_status_key = "<b>Status:</b> Dados obtidos com sucesso!"
                        self.parent.current_status_params = {}

                    self.label_status.setText(t("<b>Status:</b> Dados obtidos com sucesso!"))
                    self.btn_salvar.setEnabled(True)
                    self.exibir_dados_na_tabela()
                    return self.df
                else:
                    exibir_information(t("<b>Status:</b> Falha ao processar os dados."))
                    return None
            else:
                exibir_information(t("Nenhum dado encontrado."))
                return None
        except Exception as e:
            exibir_erro(
                self.label_status,
                t(f"Erro ao obter os dados: {e}", self.current_language)
            )
            return None
