from PySide6.QtWidgets import QSystemTrayIcon, QMenu

from services.language_utils.translations_functions import translate as t


class SystemTrayIcon(QSystemTrayIcon):
    """
    Esta classe é responsável por criar o ícone da bandeja do sistema e gerenciar as interações com ele.
    """
    # Variável de classe para armazenar a instância atual
    _instance = None

    @classmethod
    def get_instance(cls):
        """
        Retorna a instância atual do SystemTrayIcon
        """
        return cls._instance

    def __init__(self, icon, parent, window):
        super().__init__(icon, parent)
        self.window = window

        # Armazena a instância atual
        SystemTrayIcon._instance = self

        # Verifica se as notificações do sistema estão disponíveis
        # Não exibe avisos, pois isso já é tratado pelo código que tenta obter o tray_icon

        self.setup_tray()

    def setup_tray(self):
        """
        Inicializa o ícone da bandeja e o menu
        """
        self.setToolTip(t("Pratiko - ES Logistics"))

        # Cria um "menu" para o ícone da bandeja
        self.tray_menu = QMenu()

        self.show_action = self.tray_menu.addAction(t("Mostrar"))
        # Use toggle_minimize instead of show to properly restore window state
        if hasattr(self.window, 'toggle_minimize'):
            self.show_action.triggered.connect(self.window.toggle_minimize)
        else:
            self.show_action.triggered.connect(self.window.show)

        self.quit_action = self.tray_menu.addAction(t("Sair"))
        self.quit_action.triggered.connect(self.handle_quit)

        # Cria o menu para o ícone da bandeja
        self.setContextMenu(self.tray_menu)

        # Double click para mostrar a janela
        self.activated.connect(self.handle_activation)

    def update_translations(self):
        """
        Tradução quando aplicável
        """
        self.setToolTip(t("Pratiko - ES Logistics"))
        self.show_action.setText(t("Mostrar"))
        self.quit_action.setText(t("Sair"))

    def handle_activation(self, reason):
        """
        Ativação do ícone da bandeja
        """
        if reason == QSystemTrayIcon.DoubleClick:
            # Use toggle_minimize instead of show to properly restore window state
            if hasattr(self.window, 'toggle_minimize'):
                self.window.toggle_minimize()
            else:
                self.window.show()

    def handle_quit(self):
        self.window.quit_app()
        self.parent().quit()
