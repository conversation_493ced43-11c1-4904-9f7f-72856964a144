from PySide6.QtGui import (
    QPainter,
    QColor,
    QBrush,
    QPen
)

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QSystemTrayIcon

from views.ui.user_options import OptionsDialog
from views.ui.window_animations import WindowAnimations
from services.language_utils.translations_functions import (
    set_language,
    traduzir_interface,
    translate as t
)


def minimize_to_tray(self):
    """
    Minimiza a janela para a bandeja do sistema.
    Se a bandeja do sistema estiver disponível, a janela será minimizada para lá.
    """
    if hasattr(self, 'tray_icon') and self.tray_icon:
        # Checa se a mensagem já foi mostrada
        if not hasattr(self, '_has_shown_tray_message'):
            self._has_shown_tray_message = True
            self.tray_icon.showMessage(
                "Pratiko - ES Logistics",
                t("O aplicativo continuará rodando em segundo plano.\nClique duas vezes no ícone para reabrir."),
                QSystemTrayIcon.MessageIcon.Information,
                3000  # 3000ms = 3s
            )

        # Atualiza a referência do tray_icon no scheduler_service antes de esconder a janela
        if hasattr(self, 'scheduler_service') and self.scheduler_service is not None:
            self.scheduler_service.tray_icon = self.tray_icon

        self.hide()
    else:
        self.showMinimized()


def toggle_maximizar(self):
    """
    Alterna entre dois estados: normal e maximizado.
    """
    from PySide6.QtCore import Qt

    if self.isMaximized():
        # Volta para o estado normal
        self.showNormal()
        # Começa a animação
        self.setWindowOpacity(0.0)
        animation = WindowAnimations()
        animation.animate_window_open(self)
        # Muda o badge do botão
        self.btn_maximizar.setText("⬜")
    else:
        # Muda o badge do botão
        self.btn_maximizar.setText("❐")
        # Começa a animação
        self.setWindowOpacity(0.0)
        animation = WindowAnimations()
        animation.animate_window_open(self)
        # Vai para maximizado
        self.showMaximized()

    # Atualiza o estado para o toggle_minimize usando o estado da janela
    self._was_maximized = bool(self.windowState() & Qt.WindowMaximized)


def toggle_minimize(self):
    """
    Minimiza a janela para a bandeja do sistema.
    Quando clicado no ícone da barra de tarefas, restaura a janela.
    """
    from PySide6.QtCore import Qt, QTimer

    # Se a janela está minimizada ou oculta, restaura ela
    if self.isMinimized() or not self.isVisible():
        # Primeiro, mostra a janela para garantir que ela esteja visível
        self.show()

        # Restaura para o estado anterior (maximizado ou normal)
        if hasattr(self, '_was_maximized') and self._was_maximized:
            # Atualiza o texto do botão de maximizar antes de maximizar
            if hasattr(self, 'btn_maximizar'):
                self.btn_maximizar.setText("❐")
            # Maximiza a janela com um pequeno delay para garantir que funcione
            QTimer.singleShot(50, self.showMaximized)
        else:
            # Atualiza o texto do botão de maximizar antes de normalizar
            if hasattr(self, 'btn_maximizar'):
                self.btn_maximizar.setText("⬜")
            # Normaliza a janela com um pequeno delay
            QTimer.singleShot(50, self.showNormal)

        # Ativa a janela para trazer para frente com um pequeno delay
        QTimer.singleShot(100, self.activateWindow)
        QTimer.singleShot(100, lambda: self.raise_())

        # Aplica a animação de abertura
        self.setWindowOpacity(0.0)
        animation = WindowAnimations()
        animation.animate_window_open(self)
    else:
        # Guarda o estado atual antes de minimizar
        self._was_maximized = bool(self.windowState() & Qt.WindowMaximized)

        # Atualiza o texto do botão de maximizar para o próximo estado
        if hasattr(self, 'btn_maximizar'):
            if self._was_maximized:
                self.btn_maximizar.setText("❐")
            else:
                self.btn_maximizar.setText("⬜")

        # Minimiza a janela
        self.showMinimized()


def add_titlebar_widget(self, widget):
    """
    Adiciona widgets extras na barra de título.
    """
    self.extra_titlebar_widgets.append(widget)


def paintEvent(self, event):
    """
    Personaliza o evento de pintura da janela
    """
    painter = QPainter(self)
    painter.setRenderHint(QPainter.Antialiasing)
    painter.setBrush(QBrush(QColor(22, 53, 91, 255)))
    painter.setPen(QPen(Qt.NoPen))
    painter.drawRoundedRect(self.rect(), 15, 15)


def abrir_options(self):
    """
    Abre a janela de opções para selecionar colunas e idioma.
    """
    languages = ["Português", "English"]
    current_language = getattr(self, "current_language", "Português")

    # Colunas default para o dataframe
    default_columns = [
        ("Ref. cliente", "Internal Reference"),
        ("Ref. Sistema", "System Reference"),
        ("Master", "Master"),
        ("House", "House"),
        ("Solicitação de Booking", "Booking Request"),
        ("Confirmação de Booking", "Booking Confirmation"),
        ("Prontidao de carga", "Cargo Readiness"),
        ("Fornecedor de Coleta", "Collection Supplier"),
        ("Previsão de Coleta", "Collection Forecast"),
        ("Confirmação de Coleta", "Collection Confirmation"),
        ("Shipper", "Shipper"),
        ("Navio", "Vessel"),
        ("Viagem", "Voyage"),
        ("Carrier", "Carrier"),
        ("Porto de Origem", "Port of Origin"),
        ("Previsão de saída", "Departure Forecast"),
        ("Confirmação de embarque", "Shipping Confirmation"),
        ("Porto de Destino", "Destination Port"),
        ("Previsão de chegada", "Arrival Forecast"),
        ("Confirmação de chegada", "Arrival Confirmation"),
        ("Incoterm", "Incoterm"),
        ("Consignee", "Consignee"),
        ("Notify", "Notify"),
        ("Purchase Order", "Purchase Order"),
        ("Invoice", "Invoice"),
        ("Volume Containers", "Container Volume"),
        ("Transbordo", "Transshipment"),
        ("Containers", "Containers"),
    ]

    columns = list(self.df.columns) if self.df is not None else [col[0] for col in default_columns]
    selected_columns = getattr(self, "selected_columns", columns)

    dialog = OptionsDialog(
        parent = self,
        languages = languages,
        current_language = current_language,
        selected_columns = selected_columns,
        default_columns = default_columns,
        selected_filter=getattr(self, "selected_filter", None)
    )

    # Se o usuário clicar em "OK", atualiza as opções
    if dialog.exec():
        self.current_language = dialog.get_selected_language()
        self.selected_columns = dialog.get_selected_columns()

        # Não atualiza o filtro aqui, pois agora ele é gerenciado na tela principal

        # Salva as colunas para o CLI
        self.save_selected_columns()

    # Traduz a interface se o idioma for alterado
    if self.current_language != current_language:
        set_language(self.current_language)
        traduzir_interface(self)

        # Atualiza o filtro na tela principal se existir
        if hasattr(self, 'process_filter'):
            self.process_filter.update_language(self.current_language)
    return


# Eventos do mouse
def mousePressEvent(self, event):
    if event.button() == Qt.LeftButton:
        self.dragging = True
        self.offset = event.globalPosition().toPoint() - self.pos()


def mouseMoveEvent(self, event):
    if self.dragging:
        new_pos = event.globalPosition().toPoint() - self.offset
        self.move(new_pos)


def mouseReleaseEvent(self, event):
    if event.button() == Qt.LeftButton:
        self.dragging = False


def quit_app(self):
    """
    Encerrar a aplicação e salvar as chaves de API, se necessário.
    """
    if hasattr(self, 'save_api_keys'):
        self.save_api_keys()
    self.close()
