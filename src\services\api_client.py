import requests

from urllib.parse import quote

from views.utils.front_services import exibir_erro
from services.language_utils.translations_functions import translate as t


class APIClient:
    """
    Classe para interagir com a API do SkyChart.
    """

    def __init__(self, api_key):
        """
        Inicializa o cliente da API com a chave de autenticação.

        Args:
            api_key (str): A chave de autenticação da API.
        """
        self.api_key = api_key

    def obter_dados_processo(
        self,
        ds_processo=None,
        dt_inicial=None,
        dt_final=None,
        ref_cliente=None,
        nr_container=None,
    ):
        """
        Obtém dados do processo a partir da API do SkyChart.

        Args:
            ds_processo (str): Referência do processo.
            dt_inicial (str): Data inicial no formato 'dd/mm/aaaa'.
            dt_final (str): Data final no formato 'dd/mm/aaaa'.
            ref_cliente (str): Referência do cliente.
            nr_container (str): Número do container.

        Returns:
            dict: Dados obtidos da API ou None se ocorrer um erro.
        """
        base_url = (
            "https://es.skychart.com.br/apiskyline-es/api/OcsMovimento/operacional/ObterDadosPelaApiKey"
        )

        params = {
            "dsProcesso": ds_processo,
            "dtInicial": dt_inicial,
            "dtFinal": dt_final,
            "refCliente": ref_cliente,
            "nrContainer": nr_container,
        }

        # Remove parâmetros vazios
        filtered_params = {
            key: value for key, value in params.items() if value not in [None, ""]
        }

        # Caso tenha parâmetros, monta a query string(string com os parâmetros que faz parte da URL)
        if filtered_params:
            query_string = "&".join(
                f"{key}={quote(str(value))}" for key, value in filtered_params.items()
            )
            url = f"{base_url}?{query_string}"
        else:
            url = base_url

        # Cabeçalho com a chave de API
        headers = {"api-key": self.api_key}

        # Faz a requisição para a API
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            response_data = response.json()

            # Verifica se a chave "dados" está presente na resposta
            dados = response_data.get("dados", [])

            if dados:
                return response_data
            else:
                exibir_erro(None, t("Nenhum dado encontrado na resposta."))
                return None
        else:
            return None

    # Observação:
    # A complexidade da montagem da URL cresce com o número de parâmetros.
    # Para muitos parâmetros, considere o uso de **kwargs para maior flexibilidade.
