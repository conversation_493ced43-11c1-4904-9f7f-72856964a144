from PySide6.QtWidgets import (
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
)

from PySide6.QtCore import Qt

from services.language_utils.translations_functions import translate as t


class DataTable(QTableWidget):
    """
    Tabela personalizada para exibir dados.
    """

    def __init__(self, parent=None):
        """
        Inicializa a tabela de dados.
        """
        super().__init__(parent)
        self.init_ui()

    # Inicializa a interface do QTableWidget
    def init_ui(self):
        """
        Inicializa a interface do QTableWidget.
        """
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.verticalHeader().setDefaultSectionSize(25)
        self.setMinimumHeight(150)

    # Função para exibir os dados na tabela
    def display_data(self, df):
        """
        Exibe os dados do DataFrame na tabela.

        Args:
            df (pd.DataFrame): DataFrame a ser exibido na tabela.
        """
        # Limpa a tabela antes de exibir novos dados
        self.setRowCount(len(df))
        self.setColumnCount(len(df.columns))

        # Headers
        headers = [str(col) for col in df.columns]
        self.setHorizontalHeaderLabels(headers)

        # Traduzir as colunas/headers se o idioma do app estiver em English
        lang = getattr(self.parent(), "current_language", "Português")
        if lang == "English":
            translated_headers = [t(header) for header in headers]
            self.setHorizontalHeaderLabels(translated_headers)

        # Preenche a tabela com os dados do DataFrame
        for row_idx, row in df.iterrows():
            for col_idx, value in enumerate(row):
                if isinstance(value, (list, dict)):
                    display_value = str(value).replace('[', '').replace(']', '').replace("'", '')
                else:
                    display_value = str(value)

                item = QTableWidgetItem(str(display_value))
                item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row_idx, col_idx, item)

        # Ajusta a largura das colunas para o conteúdo
        self.resizeColumnsToContents()
        for i in range(self.columnCount()):
            current_width = self.columnWidth(i)
            self.setColumnWidth(i, min(max(current_width, 150), 300))
