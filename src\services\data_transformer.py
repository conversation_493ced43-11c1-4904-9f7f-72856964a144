import pandas as pd

from services.language_utils.translations_functions import translate as t
from views.utils.front_services import exibir_erro


class DataTransformer:
    """
    Classe responsável por transformar dados JSON em um DataFrame do Pandas.
    """

    @staticmethod
    def format_transbordo(value):
        """
        Formata o campo de transbordo para exibição.
        """
        if isinstance(value, (list, dict)):
            items_formatted = []
            for item in value:
                if isinstance(item, dict):
                    parts = []
                    if item.get("Porto"):
                        parts.append(f"Porto: {item['Porto']}")
                    if item.get("Navio"):
                        parts.append(f"Navio: {item['Navio']}")
                    if item.get("Viagem"):
                        parts.append(f"Viagem: {item['Viagem']}")
                    if parts:
                        items_formatted.append(", ".join(parts))
            return " → ".join(items_formatted)
        return str(value) if value else ""

    @staticmethod
    def format_containers(value):
        """
        Formata o campo de containers para exibição.
        """
        if isinstance(value, list):
            items_formatted = []
            for item in value:
                if isinstance(item, dict):
                    parts = []
                    if item.get("Container_Code"):
                        parts.append(f"Code: {item['Container_Code']}")
                    if item.get("Container tipo"):
                        parts.append(f"Tipo: {item['Container tipo']}")
                    if item.get("Container ID"):
                        parts.append(f"ID: {item['Container ID']}")
                    if parts:
                        items_formatted.append(", ".join(parts))
            return " | ".join(items_formatted)
        return str(value) if value else ""

    @staticmethod
    def json_para_dataframe(json_data):
        """
        Converte dados JSON em um DataFrame do Pandas, aplicando as formatações específicas.
        
        Args:
            json_data (dict): Dados JSON a serem transformados.
            
        Returns:
            pd.DataFrame: DataFrame resultante da transformação.
        """
        dados = json_data.get("dados", None)

        # Verifica se os dados estão vazios ou nulos
        if not dados:
            exibir_erro(None, t("Dados vazios ou nulos."))
            return None

        # Verifica se os dados são uma lista ou dicionário
        if isinstance(dados, dict):
            dados = [dados]
        elif not isinstance(dados, list):
            exibir_erro(
                None,
                t(
                    'O campo "dados" não é uma lista ou dicionário. Tipo recebido: {tipo}',
                ).format(tipo=type(dados)),
            )
            return None

        processed_data = []
        for item in dados:
            item_flat = {}
            for key, value in item.items():
                if key == "Transbordo":
                    item_flat[key] = DataTransformer.format_transbordo(value)
                elif key == "Containers":
                    item_flat[key] = DataTransformer.format_containers(value)
                elif isinstance(value, (list, dict)):
                    item_flat[key] = str(value)  # Converte listas e dicionários para string
                else:
                    item_flat[key] = value
            processed_data.append(item_flat)

        df = pd.DataFrame(processed_data)

        ordered_columns = [
            "Ref. cliente",
            "Ref. Sistema",
            "Master",
            "House",
            "Solicitação de Booking",
            "Confirmação de Booking",
            "Prontidao de carga",
            "Fornecedor de Coleta",
            "Previsão de Coleta",
            "Confirmação de Coleta",
            "Shipper",
            "Navio",
            "Viagem",
            "Carrier",
            "Porto de Origem",
            "Previsão de saída",
            "Confirmação de embarque",
            "Porto de Destino",
            "Previsão de chegada",
            "Confirmação de chegada",
            "Incoterm",
            "Consignee",
            "Notify",
            "Purchase Order",
            "Invoice",
            "Fornecedor de Entrega",
            "Previsão de Entrega",
            "Confirmação de Entrega",
            "Volume Containers",
            "Transbordo",
            "Containers",
        ]

        # Reordena as colunas, se existirem
        existing_columns = [col for col in ordered_columns if col in df.columns]

        # Cria um novo DF
        df = df[existing_columns]

        # Dropa colunas que não estão na lista de colunas ordenadas
        df = df.dropna(axis=1, how="all")
        df = df.reset_index(drop=True)

        return df
