from PySide6.QtCore import QTime
from PySide6.QtWidgets import QDialog

from services.scheduler_service import SchedulerService
from services.language_utils.translations_functions import(
    translate as t,
    get_system_language
)
from views.utils.front_services import exibir_information
from views.ui.widgets.time_input_widget import TimeInputWidget
from views.ui.scheduler.scheduler_ui import SchedulerUI
from views.ui.scheduler.scheduler_data import SchedulerData


class SchedulerDialog(QDialog):
    """
    Classe para o agendador automático de exportação.
    Permite adicionar/remover horários e dias da semana para execução automática.
    """

    MAX_HOURS = 4

    def __init__(self, parent=None, api_key=None, company_name=None):
        """
        Inicializa o agendador de tarefas.

        Args:
            parent: (QWidget, optional): <PERSON><PERSON>, se houver. Default to None.
            api_key: (str, optional): Chave de <PERSON>. Default to None.
        """
        super().__init__(parent)
        self.api_key = api_key
        self.company_name = company_name
        self.scheduler_service = SchedulerService(api_key=api_key)
        self.schedule_id = None  # Inicializa o ID do agendamento como None

        # Obtém o idioma atual da aplicação
        self.current_language = None
        parent_window = parent
        while parent_window:
            if hasattr(parent_window, 'current_language'):
                self.current_language = parent_window.current_language
                break
            parent_window = parent_window.parent()

        # Se não encontrou o idioma no parent, usa o idioma do sistema
        if not self.current_language:
            self.current_language = get_system_language()

        # Força o idioma para Português se não for explicitamente English
        if self.current_language != "English":
            self.current_language = "Português"

        # Monitora mudanças no idioma - inicializa com None para forçar atualização
        self._original_language = None

        self.available_companies = {}
        parent_window = parent
        while parent_window:
            if hasattr(parent_window, 'api_keys'):
                self.available_companies = parent_window.api_keys
                break
            parent_window = parent_window.parent()

        # Inicializa os componentes
        self.ui = SchedulerUI(self)
        self.data = SchedulerData(self)
        
        # Configura a interface
        self.ui.setup_ui()
        self.data.load_saved_schedule()

        # Força a atualização dos widgets de horário após carregar
        self.update_time_widgets_language()

    # Métodos para gerenciamento de inputs de horário
    def add_time_input(self):
        """
        Adiciona um novo campo de horário
        """
        if self.ui.times_container.count() >= self.MAX_HOURS:
            return

        # Passa o idioma atual para o TimeInputWidget
        current_lang = self.current_language if hasattr(self, 'current_language') and self.current_language else get_system_language()

        # Força o idioma para Português se não for explicitamente English
        if current_lang != "English":
            current_lang = "Português"

        # Todos os widgets podem ser deletados
        time_input = TimeInputWidget(can_delete=True, language=current_lang)

        # Garante que o dropdown está configurado corretamente
        time_input.populate_time_options()

        # Define um horário padrão (01:00 para inglês, 12:00 para português)
        if current_lang == "English":
            default_time = QTime(1, 0)  # 1:00 AM para inglês
        else:
            default_time = QTime(12, 0)  # 12:00 para português

        # Define o horário no widget
        time_input.setTime(default_time)

        self.ui.times_container.addWidget(time_input)
        self.update_add_button_visibility()

    def remove_time_input(self, widget):
        """
        Método centralizado para gerenciar remoção de widget e visibilidade do botão
        """
        widget.setParent(None)
        widget.deleteLater()

        # Atualiza a visibilidade do botão
        self.ui.times_container.update()
        self.update_add_button_visibility()

    def update_add_button_visibility(self):
        """
        Método centralizado para gerenciar a visibilidade do botão de adicionar
        """
        # Conta quantos widgets (TimeInputWidget) existem
        count = len([
            i for i in range(self.ui.times_container.count())
            if isinstance(self.ui.times_container.itemAt(i).widget(), TimeInputWidget)
        ])

        # Se o count for menor que o máximo de horas, mostra o botão de adicionar
        is_enabled = count < self.MAX_HOURS
        self.ui.add_time_container.setEnabled(is_enabled)

        # Apenas habilita ou desabilita o botão - o estilo já tem regras para o estado desabilitado
        self.ui.add_time_button.setEnabled(is_enabled)

    # Manipuladores de eventos
    def on_save_clicked(self):
        """
        Handler para o evento de clique no botão de salvar
        """
        # Verifica se pelo menos um dia da semana está selecionado
        selected_days = [check.isChecked() for check in self.ui.day_checks]
        if not any(selected_days):
            exibir_information(
                self,
                t("Selecione pelo menos um dia da semana.")
            )
            return

        # Tenta salvar o agendamento
        if self.data.save_schedule():
            exibir_information(
                self,
                t("Agendamento salvo com sucesso.")
            )
            self.accept()

    def open_scheduler_list(self):
        """
        Abre a lista de agendamentos
        """
        # Pra não dar circular import
        from views.ui.scheduler_list_dialog import SchedulerListDialog

        api_key = self.scheduler_service.get_api_key()

        dialog = SchedulerListDialog(self, api_key=api_key)
        dialog.setWindowTitle(t("Gerenciador de Agendamentos"))
        if dialog.exec():
            # Reload no scheduler caso tenha sido excluído um agendamento
            self.data.load_saved_schedule()

    def clear_all(self):
        """
        Limpa todos os horários e desmarca todos os dias da semana
        """
        # Remove todos os horários
        for i in reversed(range(self.ui.times_container.count())):
            widget = self.ui.times_container.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Atualiza a visibilidade do botão
        self.update_add_button_visibility()

        # Remove todos os dias da semana
        for check in self.ui.day_checks:
            check.setChecked(False)

    def update_time_widgets_language(self):
        """
        Atualiza o idioma de todos os widgets de horário.
        """
        # Atualiza o idioma original
        self._original_language = self.current_language

        # Força o idioma para Português se não for explicitamente English
        if self.current_language != "English":
            self.current_language = "Português"

        # Atualiza todos os widgets de horário
        for i in range(self.ui.times_container.count()):
            widget = self.ui.times_container.itemAt(i).widget()
            if isinstance(widget, TimeInputWidget):
                widget.update_language(self.current_language)

    def on_company_changed(self, company_name):
        """
        Handler para o evento de mudança de empresa
        """
        self.company_name = company_name
        if company_name in self.available_companies:
            self.api_key = self.available_companies[company_name]
            self.scheduler_service.set_api_key(self.api_key, self.company_name)
            
    def load_schedule_data(self, schedule):
        """
        Carrega os dados de um agendamento específico.
        
        Args:
            schedule (dict): Dados do agendamento.
        """
        self.data.load_schedule_data(schedule)
