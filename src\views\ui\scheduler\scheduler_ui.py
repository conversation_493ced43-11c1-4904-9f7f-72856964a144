from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QCheckBox,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QSizePolicy,
    QVBoxLayout,
    QWidget,
    QGridLayout,
    QComboBox
)

from services.language_utils.translations_functions import translate as t
from views.utils.widget_style import WidgetStyle


class SchedulerUI:
    """
    Classe responsável pela configuração da interface do usuário do agendador.
    """
    
    def __init__(self, dialog):
        """
        Inicializa a interface do usuário do agendador.
        
        Args:
            dialog: Instância do SchedulerDialog que contém esta UI.
        """
        self.dialog = dialog
        self.times_container = None
        self.add_time_container = None
        self.add_time_button = None
        self.add_time_label = None
        self.save_button = None
        self.company_combo = None
        self.day_checks = []
        
    def setup_ui(self):
        """
        Configura a interface do usuário.
        """
        # Estilo do background e tamanho da janela
        self.dialog.resize(400, 350)
        self.dialog.setStyleSheet(WidgetStyle().background_style())

        # Layout principal
        main_layout = QVBoxLayout(self.dialog)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # Seleção de empresa
        company_section = QWidget()
        company_layout = QHBoxLayout(company_section)
        company_layout.setContentsMargins(0, 0, 0, 10)
        company_layout.setSpacing(10)

        # Label para a seleção da empresa
        company_label = QLabel(t("Empresa:"))
        company_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        company_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        company_layout.addWidget(company_label)

        # ComboBox (dropdown) para selecionar a empresa
        self.company_combo = QComboBox()
        self.company_combo.addItems(self.dialog.available_companies.keys())
        self.company_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # Se setup_ui.company_name E setup_ui.company_name está nas empresas disponíveis, seleciona a empresa
        if self.dialog.company_name and self.dialog.company_name in self.dialog.available_companies:
            self.company_combo.setCurrentText(self.dialog.company_name)
        # Se não, seleciona a primeira empresa disponível
        elif self.dialog.available_companies:
            first_company = list(self.dialog.available_companies.keys())[0]
            self.company_combo.setCurrentText(first_company)
            self.dialog.company_name = first_company

        # Conecta o evento de mudança de empresa
        self.company_combo.currentTextChanged.connect(self.dialog.on_company_changed)

        # Adiciona o ComboBox ao layout
        company_layout.addWidget(self.company_combo)
        main_layout.addWidget(company_section)

        # Layout de conteúdo para os widgets
        content_layout = QGridLayout()
        content_layout.setSpacing(20)

        # Cabeçalho de horas
        hours_label = QLabel(t("Horários de Execução:"))
        hours_label.setMinimumHeight(25)
        hours_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        hours_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        content_layout.addWidget(hours_label, 0, 0)

        # Cabeçalho de dias
        days_label = QLabel(t("Dias da Semana:"))
        days_label.setMinimumHeight(25)
        days_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        days_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        content_layout.addWidget(days_label, 0, 1)

        # Adiciona o layout de conteúdo ao layout principal
        main_layout.addLayout(content_layout)

        # Container esquerdo (horários) - sem o cabeçalho
        left_container = QWidget()
        left_container.setObjectName("borderedWidget")
        left_container.setStyleSheet(WidgetStyle().left_container_style())
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(5)

        # Cria um widget de altura fixa para a área de horários
        times_area = QWidget()
        times_area.setFixedHeight(120)  # Altura fixa para a área de horários
        times_area_layout = QVBoxLayout(times_area)
        times_area_layout.setContentsMargins(0, 0, 0, 0)
        times_area_layout.setSpacing(5)

        # Container para os horários
        times_widget = QWidget()
        self.times_container = QVBoxLayout(times_widget)
        self.times_container.setSpacing(10)
        self.times_container.setContentsMargins(0, 0, 0, 0)
        self.times_container.setAlignment(Qt.AlignTop)
        times_area_layout.addWidget(times_widget)

        # Adiciona a área de horários ao layout esquerdo
        left_layout.addWidget(times_area)

        # Container fixo para "Adicionar mais horários"
        add_hours_widget = QWidget()
        add_hours_widget.setFixedHeight(40)
        add_hours_layout = QVBoxLayout(add_hours_widget)
        add_hours_layout.setContentsMargins(0, 0, 0, 0)

        # Container para adicionar mais horários
        self.add_time_container = QWidget()

        # Layout base
        add_time_layout = QHBoxLayout(self.add_time_container)
        add_time_layout.setContentsMargins(0, 5, 0, 0)
        add_time_layout.setSpacing(5)

        # Botão para adicionar mais horários
        self.add_time_button = QPushButton("+")
        self.add_time_button.setFixedSize(20, 20)
        self.add_time_button.setStyleSheet(WidgetStyle().add_button_style())
        self.add_time_button.clicked.connect(self.dialog.add_time_input)

        # Label para adicionar mais horários
        self.add_time_label = QLabel(t("Adicionar Horários:"))
        self.add_time_label.setStyleSheet("font-weight: bold; color: white; font-size: 12px;")
        add_time_layout.addWidget(self.add_time_button)
        add_time_layout.addWidget(self.add_time_label)
        add_time_layout.addStretch()

        # Adiciona o container ao widget fixo
        add_hours_layout.addWidget(self.add_time_container)

        # Adiciona o widget fixo ao layout esquerdo
        left_layout.addWidget(add_hours_widget)

        # Visibilidade do botão de adicionar horário
        self.dialog.update_add_button_visibility()

        # Layout para os botões
        buttons_widget = QWidget()
        buttons_widget.setFixedHeight(40)  # Altura fixa
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 5, 0, 0)

        # Layout do botão de salvar
        self.save_button = QPushButton(t("Salvar"))
        self.save_button.clicked.connect(self.dialog.on_save_clicked)
        self.save_button.setFixedWidth(80)

        # Botão de limpar tudo
        clear_all_button = QPushButton(t("Limpar Tudo"))
        clear_all_button.clicked.connect(self.dialog.clear_all)
        clear_all_button.setFixedWidth(100)

        # Adiciona os botões ao layout
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(clear_all_button)
        buttons_layout.addStretch()

        # Adiciona o widget de botões ao layout esquerdo
        left_layout.addWidget(buttons_widget)

        # Container direito (dias)
        right_container = QWidget()
        right_container.setObjectName("borderedWidget")
        right_container.setStyleSheet(WidgetStyle().right_container_style())
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(5)

        # Container para os dias
        days_widget = QWidget()
        days_layout = QVBoxLayout(days_widget)
        days_layout.setSpacing(8)
        days_layout.setContentsMargins(0, 0, 0, 0)
        days_layout.setAlignment(Qt.AlignTop)

        self.day_checks = []
        days = ["Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado", "Domingo"]
        for day in days:
            check = QCheckBox(t(day))
            check.setMinimumHeight(25)
            self.day_checks.append(check)
            days_layout.addWidget(check)

        # Adiciona o widget de dias ao layout direito
        right_layout.addWidget(days_widget)

        # Layout final, junta os containers
        content_layout.addWidget(left_container, 1, 0, Qt.AlignTop)
        content_layout.addWidget(right_container, 1, 1, Qt.AlignTop)

        # Define o espaço entre linhas e colunas
        content_layout.setVerticalSpacing(10)  # Espaço entre cabeçalho e container
        content_layout.setHorizontalSpacing(20)  # Espaço entre containers esquerdo e direito
