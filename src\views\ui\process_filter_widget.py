from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QWidget,
    QLabel,
    QHBoxLayout,
    QRadioButton,
    QButtonGroup
)

from services.language_utils.translations_functions import translate as t
from views.utils.front_services import user_checks
from views.utils.widget_style import WidgetStyle


class ProcessFilterWidget(QWidget):
    """
    Widget para seleção do tipo de processo a ser exibido.
    """

    # Sinal emitido quando o filtro é alterado
    filter_changed = Signal(str)

    def __init__(self, parent=None, selected_filter=None, current_language="Português"):
        """
        Inicializa o widget de seleção de filtro de processos.

        Args:
            parent (QWidget, optional): Widget pai. Defaults to None.
            selected_filter (str, optional): Filtro selecionado inicialmente. Defaults to None.
            current_language (str, optional): Idioma atual. Defaults to "Português".
        """
        super().__init__(parent)
        self.selected_filter = selected_filter
        self.current_language = current_language
        self.init_ui()

    def init_ui(self):
        """
        Configura a interface do usuário.
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Label para o filtro
        '''filter_label = QLabel(t("Selecione o tipo de processo:", self.current_language))
        filter_label.setStyleSheet("color: white; font-size: 14px;")
        layout.addWidget(filter_label)'''

        # Grupo de botões para garantir que apenas um seja selecionado
        self.button_group = QButtonGroup(self)
        self.radio_buttons = {}

        # Estilo para os radio buttons
        radio_style = (WidgetStyle().radio_style())

        # Adiciona os radio buttons
        checks = user_checks()
        for check in checks:
            translated_text = t(check, self.current_language)
            radio = QRadioButton(translated_text)
            radio.setStyleSheet(radio_style)

            # Ensure the radio button has enough width for the text
            radio.setMinimumWidth(150)

            # Verifica se há algum filtro selecionado, caso não, default em "Todos os Processos"
            if (self.selected_filter and translated_text == self.selected_filter) or \
                (not self.selected_filter and check == "Todos os Processos"):
                radio.setChecked(True)

            # Adiciona ao layout e ao grupo
            layout.addWidget(radio)
            self.button_group.addButton(radio)
            self.radio_buttons[check] = radio

        # Conecta o evento de alteração do botão
        self.button_group.buttonClicked.connect(self.on_button_clicked)

        # Adiciona um espaçador para empurrar os botões para a esquerda
        layout.addStretch()

    def on_button_clicked(self, button):
        """
        Emite o sinal de alteração quando um botão é clicado.

        Args:
            button (QRadioButton): Botão que foi clicado.
        """
        # Emite o sinal com o texto do botão selecionado
        self.filter_changed.emit(button.text())

    def get_selected_filter(self):
        """
        Retorna o filtro selecionado.

        Returns:
            str: Texto do filtro selecionado ou None se nenhum estiver selecionado.
        """
        checked_button = self.button_group.checkedButton()
        if checked_button:
            return checked_button.text()
        return None

    def set_selected_filter(self, filter_text):
        """
        Define o filtro selecionado.

        Args:
            filter_text (str): Texto do filtro a ser selecionado.
        """
        # Procura o botão com o texto correspondente
        for check, radio in self.radio_buttons.items():
            translated_text = t(check, self.current_language)
            if translated_text == filter_text:
                radio.setChecked(True)
                return

    def update_language(self, language):
        """
        Atualiza o idioma dos textos.

        Args:
            language (str): Novo idioma.
        """
        self.current_language = language

        # Atualiza o texto do label
        self.layout().itemAt(0).widget().setText(t("Selecione o tipo de processo:", language))

        # Guarda o filtro selecionado atual (o tipo de processo, não o texto traduzido)
        selected_key = None
        for check, radio in self.radio_buttons.items():
            if radio.isChecked():
                selected_key = check
                break

        # Atualiza os textos dos botões
        for check, radio in self.radio_buttons.items():
            translated_text = t(check, language)
            radio.setText(translated_text)

            # Mantém o mesmo filtro selecionado após a tradução
            if selected_key and check == selected_key:
                radio.setChecked(True)
