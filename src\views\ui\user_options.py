from PySide6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QComboBox,
    QLabel,
    QListWidget,
    QListWidgetItem,
)

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QPushButton, QHBoxLayout

from views.utils.front_services import exibir_information
from services.language_utils.translations_functions import translate as t
from views.utils.widget_style import WidgetStyle


class OptionsDialog(QDialog):
    """
    Classe para selecionar opções de configuração.
    """

    def __init__(
            self,
            parent=None,
            languages=None,
            current_language=None,
            selected_columns=None,
            default_columns=None,
            selected_filter=None,  # Mantido para compatibilidade, mas não é mais usado
        ):
        """
        Inicializa o diálogo de opções do usuário.
        """
        super().__init__(parent)
        self.setWindowTitle(t("Configurações"))
        self.resize(400, 300)
        layout = QVBoxLayout(self)

        # Estilo do background
        self.setStyleSheet(WidgetStyle().background_style())

        # Seletor de linguagem highlighted
        lang_label = QLabel(t("Selecione a linguagem:"))
        layout.addWidget(lang_label)

        # ComboBox para selecionar a linguagem
        self.language_combo = QComboBox()
        self.language_combo.addItems(languages)
        if current_language:
            self.language_combo.setCurrentText(current_language)
        layout.addWidget(self.language_combo)

        # Seletor de colunas
        col_label = QLabel(t("Selecione as colunas para exportar:", current_language))
        layout.addWidget(col_label)

        # Seletor com checkbox
        self.column_list = QListWidget()

        # Adiciona colunas com checkbox
        for pt, en in default_columns:
            if current_language == "English":
                display_test = t(en, current_language)
            else:
                display_test = t(pt, current_language)
            item = QListWidgetItem(display_test)
            item.setData(Qt.UserRole, pt) # Nome em PT para uso interno
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Checked if pt in selected_columns else Qt.Unchecked)
            self.column_list.addItem(item)
        layout.addWidget(self.column_list)

        # Botões de ação
        clear_all = QPushButton(t("Limpar Tudo", current_language))
        select_all = QPushButton(t("Selecionar Tudo", current_language))
        open_scheduler = QPushButton(t("Agendamentos", current_language))
        ok_button = QPushButton(t("OK", current_language))
        cancel_button = QPushButton(t("Cancelar", current_language))

        # Adiciona os botões
        buttons = QHBoxLayout()
        buttons.addWidget(open_scheduler)
        buttons.addWidget(clear_all)
        buttons.addWidget(select_all)
        buttons.addWidget(ok_button)
        buttons.addWidget(cancel_button)

        # Adiciona os botões ao layout principal
        layout.addLayout(buttons)

        # Conecta os botões às funções
        clear_all.clicked.connect(self.clear_all_columns)
        select_all.clicked.connect(self.select_all_columns)
        open_scheduler.clicked.connect(self.open_scheduler_list)

        # Conecta os botões de OK e Cancelar
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        # Inicialmente, esconde o botão de selecionar tudo
        select_all.hide()

        # Atualiza a visibilidade do botão de selecionar tudo
        self.column_list.itemChanged.connect(lambda: self.toggle_select_all(select_all, clear_all))

        # Garante que iniciará
        self.toggle_select_all(select_all, clear_all)

    def toggle_select_all(self, select_all, clear_all):
        """
        Alterna entre selecionar e desmarcar todas as colunas.
        """
        all_unchecked = all(
            self.column_list.item(i).checkState() == Qt.Unchecked
            for i in range(self.column_list.count())
        )

        if all_unchecked:
            select_all.show()
            clear_all.hide()
        else:
            select_all.hide()
            clear_all.show()

    def clear_all_columns(self):
        for i in range(self.column_list.count()):
            self.column_list.item(i).setCheckState(Qt.Unchecked)

    def select_all_columns(self):
        for i in range(self.column_list.count()):
            self.column_list.item(i).setCheckState(Qt.Checked)

    def accept(self):
        """
        Valida e aceita as opções selecionadas.
        """
        selected_columns = self.get_selected_columns()
        if selected_columns == []:
            exibir_information(self, t("Selecione pelo menos uma coluna."))
            return
        if selected_columns is not None:
            exibir_information(self, t("Opções salvas com sucesso."))
            super().accept()

    def get_selected_language(self):
        return self.language_combo.currentText()

    def get_selected_columns(self):
        return [
            self.column_list.item(i).data(Qt.UserRole)
            for i in range(self.column_list.count())
            if self.column_list.item(i).checkState() == Qt.Checked
        ]

    '''def get_selected_filter(self):
        """
        Retorna o filtro selecionado.

        Note: Esta função agora retorna None pois o filtro foi movido para a tela principal.
        Mantida para compatibilidade com o código existente.
        """
        # Retorna None pois o filtro foi movido para a tela principal
        return None'''

    def open_scheduler_list(self):
        """
        Abre o gerenciador de agendamentos
        """
        api_key = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'api_key'):
                api_key = parent.api_key
                break
            parent = parent.parent()

        # Importado aqui para evitar circular import
        from views.ui.scheduler_list_dialog import SchedulerListDialog

        # Abre o gerenciador de agendamentos
        dialog = SchedulerListDialog(self, api_key=api_key)
        dialog.setWindowTitle(t("Gerenciador de Agendamentos"))
        dialog.exec()
