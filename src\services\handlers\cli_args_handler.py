import argparse
import sys

def parse_arguments():
    """
    <PERSON><PERSON><PERSON> os argumentos da linha de comando.
    """
    parser = argparse.ArgumentParser(description='Pratiko - Data Export Tool')
    
    # Argumentos obrigatórios
    parser.add_argument(
        '--dt-inicial', 
        required='--cli' in sys.argv,
        help='Data inicial (formato: dd/mm/aaaa ou yyyy-mm-dd)'
    )
    parser.add_argument(
        '--dt-final', 
        required='--cli' in sys.argv,
        help='Data final (formato: dd/mm/aaaa ou yyyy-mm-dd)'
    )
    
    # Argumentos opcionais
    parser.add_argument(
        '--api-key', 
        help='API key para autenticação. Se não fornecida, será usada a chave padrão do arquivo keys.json'
    )
    parser.add_argument(
        '--output-dir', 
        default='exports', 
        help='Diretório para salvar o arquivo (default: Downloads)'
    )
    parser.add_argument(
        '--output-format',
        choices=['csv', 'xlsx'],
        default='xlsx',
        help='Formato do arquivo de saída (default: xlsx)'
    )
    return parser.parse_args()
