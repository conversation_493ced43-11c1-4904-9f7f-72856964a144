from PySide6.QtWidgets import (
    QGroupBox, 
    QVBoxLayout, 
    QFormLayout, 
    QLineEdit, 
    QLabel,
)

from views.utils.date_utils import formatar_data

from services.language_utils.translations_functions import translate as t


class FormInputs(QGroupBox):
    """
    Formulário de entrada para os parâmetros de consulta.
    """

    def __init__(self, parent=None):
        """
        Inicializa o formulário de entrada.
        """
        super().__init__( parent)
        self.init_ui()

    def init_ui(self):
        """
        Configura a interface do usuário.
        """
        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # Configuração para o translator
        self.label_ref_processo = QLabel(t("Referência do Processo ES:"))
        self.label_data_inicial = QLabel(t("Data Inicial (dd/mm/aaaa):"))
        self.label_data_final = QLabel(t("Data Final (dd/mm/aaaa):"))
        self.label_ref_interna = QLabel(t("Referência Cliente:"))
        self.label_num_container = QLabel(t("Número do Container:"))

        # Campos de entrada para os parâmetros
        self.entry_processo = QLineEdit(self)
        self.entry_dt_inicial = QLineEdit(self)
        self.entry_dt_final = QLineEdit(self)
        self.entry_ref_cliente = QLineEdit(self)
        self.entry_nr_container = QLineEdit(self)

        # Definindo largura mínima para os campos de entrada
        for entry in [
            self.entry_processo, 
            self.entry_dt_inicial, 
            self.entry_dt_final, 
            self.entry_ref_cliente, 
            self.entry_nr_container
        ]:
            entry.setMinimumWidth(800)

        # Conectando o evento de edição de texto para formatar a data
        self.entry_dt_inicial.textEdited.connect(
            lambda text: formatar_data(text, self.entry_dt_inicial)
        )
        self.entry_dt_final.textEdited.connect(
            lambda text: formatar_data(text, self.entry_dt_final)
        )

        form_layout.addRow(self.label_ref_processo, self.entry_processo)
        form_layout.addRow(self.label_data_inicial, self.entry_dt_inicial)
        form_layout.addRow(self.label_data_final, self.entry_dt_final)
        form_layout.addRow(self.label_ref_interna, self.entry_ref_cliente)
        form_layout.addRow(self.label_num_container, self.entry_nr_container)

        layout.addLayout(form_layout)
        self.setLayout(layout)
