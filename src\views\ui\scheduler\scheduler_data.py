from PySide6.QtCore import QTime

from services.language_utils.translations_functions import translate as t
from views.utils.front_services import exibir_information
from views.ui.widgets.time_input_widget import TimeInputWidget


class SchedulerData:
    """
    Classe responsável pelo gerenciamento de dados do agendador.
    """
    
    def __init__(self, dialog):
        """
        Inicializa o gerenciador de dados do agendador.
        
        Args:
            dialog: Instância do SchedulerDialog que contém este gerenciador.
        """
        self.dialog = dialog
        
    def load_saved_schedule(self):
        """
        Carrega e aplica a configuração salva do agendador
        """
        config = self.dialog.scheduler_service.load_schedules()
        if config and 'execution_times' in config and config['execution_times']:
            times = config['execution_times']

            # para o index em ordem decrescente, remove todos os widgets caso existam
            for index in reversed(range(self.dialog.ui.times_container.count())):
                widget = self.dialog.ui.times_container.itemAt(index).widget()
                if widget:
                    widget.deleteLater()

            # Força o idioma para Português se não for explicitamente English
            if self.dialog.current_language != "English":
                self.dialog.current_language = "Português"

            # Adiciona os horários
            for time_str in times:
                # Passa o idioma atual para o TimeInputWidget
                current_lang = self.dialog.current_language

                # Cria o widget com o idioma correto
                time_input = TimeInputWidget(can_delete=True, language=current_lang) # Inputs podem ser deletados

                # Garante que o dropdown está configurado corretamente
                time_input.populate_time_options()

                # Configura o horário
                time_parts = time_str.split(':')
                time = QTime(int(time_parts[0]), int(time_parts[1]))

                # Define o horário no widget
                time_input.setTime(time)

                # Adiciona o widget ao container
                self.dialog.ui.times_container.addWidget(time_input)

            # Atualiza a visibilidade do botão, caso tenha algum horário
            self.dialog.update_add_button_visibility()

            # Seta os dias para que sejam marcados
            for check, day in zip(self.dialog.ui.day_checks, config['days']):
                check.setChecked(day)
        else:
            self.dialog.update_add_button_visibility()
            
    def save_schedule(self):
        """
        Salva as configurações do agendador.
        """
        try:
            # Pega a empresa
            selected_company = self.dialog.ui.company_combo.currentText()

            if selected_company in self.dialog.available_companies:
                self.dialog.api_key = self.dialog.available_companies[selected_company]
                self.dialog.scheduler_service.set_api_key(self.dialog.api_key, selected_company)

            # Pega todos os horários configurados dos widgets de tempo/hora
            times = []

            for i in range(self.dialog.ui.times_container.count()):
                widget = self.dialog.ui.times_container.itemAt(i).widget()

                if isinstance(widget, TimeInputWidget):
                    # Pega o texto diretamente do combobox
                    combo_text = widget.time_combo.currentText()

                    # Também usa o time_edit que é atualizado pelo dropdown
                    time_obj = widget.time_edit.time()

                    # Formata o horário como "HH:MM"
                    formatted_time = f"{time_obj.hour():02d}:{time_obj.minute():02d}"

                    # Adiciona o horário formatado à lista
                    times.append(formatted_time)

                    # Força uma atualização do time_edit com o texto atual do combobox
                    widget.update_time_edit(combo_text)

            # Pega os dias da semana e armazena na variável selected_days
            selected_days = [check.isChecked() for check in self.dialog.ui.day_checks]

            # Caso nenhum horário for selecionado, desativa os agendamentos
            if not times:
                exibir_information(
                    self.dialog,
                    t("Todos os agendamentos foram desativados."),
                )
                # Salva a configuração vazia e remove todos os jobs agendados
                success = self.dialog.scheduler_service.save_schedule(
                    execution_times=[],
                    selected_days=selected_days,
                    schedule_id=self.dialog.schedule_id
                )
                if success:
                    self.dialog.scheduler_service.scheduler.remove_all_jobs()
                return success

            # Salva a configuração com os horários e dias selecionados
            success = self.dialog.scheduler_service.save_schedule(
                execution_times=times,
                selected_days=selected_days,
                schedule_id=self.dialog.schedule_id
            )

            return success

        except Exception as e:
            exibir_information(
                self.dialog,
                t("Erro ao salvar configuração: {error}").format(error=str(e))
            )
            return False
            
    def load_schedule_data(self, schedule):
        """
        Carrega os dados de um agendamento específico.

        Args:
            schedule (dict): Dados do agendamento.
        """
        # Seta o ID do agendamento
        self.dialog.schedule_id = schedule.get("id")

        # Seta a empresa
        company = schedule.get("selected_company")
        if company and hasattr(self.dialog, 'ui') and hasattr(self.dialog.ui, 'company_combo') and company in self.dialog.available_companies:
            self.dialog.ui.company_combo.setCurrentText(company)

        # Dá load nos horários
        times = schedule.get("execution_times", [])

        # Dá clear nos horários existentes
        for i in reversed(range(self.dialog.ui.times_container.count())):
            widget = self.dialog.ui.times_container.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Força o idioma para Português
        if self.dialog.current_language != "English":
            self.dialog.current_language = "Português"

        # Adiciona os horários
        for time_str in times:
            # Passa o idioma atual para o TimeInputWidget
            current_lang = self.dialog.current_language

            # Cria o widget com o idioma correto
            time_input = TimeInputWidget(can_delete=True, language=current_lang)

            # Garante que o dropdown está configurado corretamente
            time_input.populate_time_options()

            # Configura o horário
            time_parts = time_str.split(':')
            time = QTime(int(time_parts[0]), int(time_parts[1]))

            # Define o horário no widget
            time_input.setTime(time)

            # Adiciona o widget ao container
            self.dialog.ui.times_container.addWidget(time_input)

        # Da update na visibilidade
        self.dialog.update_add_button_visibility()

        # Seta os dias
        days = schedule.get("days", [False] * 7)
        for check, day in zip(self.dialog.ui.day_checks, days):
            check.setChecked(day)
