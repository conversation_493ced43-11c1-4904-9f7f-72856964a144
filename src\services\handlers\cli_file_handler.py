import os
from datetime import datetime
import pandas as pd

def cli_save_data(data, output_dir, file_format='csv', company_name=None):
    """
    Salva os dados em um arquivo com timestamp no diretório especificado.

    Args:
        data (dict ou pd.DataFrame): Dados para salvar, como dicionário ou DataFrame
        output_dir (str): Diretório onde salvar o arquivo
        file_format (str): Formato para salvar ('csv' ou 'xlsx')
        company_name (str, optional): Nome da empresa para incluir no nome do arquivo

    Returns:
        str: Caminho do arquivo salvo se bem sucedido, None caso contrário
    """
    try:
        if data is None:
            print("Erro: Nenhum dado para salvar.")
            return None

        # Converte para DataFrame se receber um dicionário
        if isinstance(data, dict):
            if 'dados' in data:
                df = pd.DataFrame(data['dados'])
            else:
                df = pd.DataFrame(data)
        elif isinstance(data, pd.DataFrame):
            df = data
        else:
            print(f"Erro: Tipo de dado não suportado {type(data)}")
            return None

        # Cria o diretório de saída se não existir
        os.makedirs(output_dir, exist_ok=True)

        # Gera nome do arquivo com timestamp e nome da empresa
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        date_formatted = datetime.now().strftime('%d-%m-%Y')

        if company_name:
            # Remove caracteres inválidos para nome de arquivo
            safe_company_name = ''.join(c for c in company_name if c.isalnum() or c in ' -_')
            base_filename = f"{safe_company_name} - {date_formatted}"
        else:
            base_filename = f"report_{timestamp}"

        # Verifica se o arquivo já existe e adiciona um sufixo se necessário
        counter = 1
        filename = f"{base_filename}.{file_format}"
        filepath = os.path.join(output_dir, filename)

        while os.path.exists(filepath):
            filename = f"{base_filename} ({counter}).{file_format}"
            filepath = os.path.join(output_dir, filename)
            counter += 1

        # Salva baseado no formato
        if file_format == 'csv':
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
        else:  # xlsx
            df.to_excel(filepath, index=False, engine='openpyxl')

        return filepath

    except Exception as e:
        print(f"Erro ao salvar arquivo: {str(e)}")
        return None
