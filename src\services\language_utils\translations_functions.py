import locale

from views.language_utils.translation_dict import translations


def set_language(language):
    """
    Define o idioma atual do aplicativo.
    """
    global _current_language
    _current_language = language


def get_system_language():
    """
    Obtém o idioma do sistema operacional.

    Returns:
        str: O idioma do sistema, como "pt" para português ou "en" para inglês.
    """
    lang, _ = locale.getdefaultlocale()
    if lang is None:
        return "Português"  # Valor padrão se não conseguir obter o idioma do sistema
    if lang.startswith("pt"):
        return "Português"
    elif lang.startswith("en"):
        return "English"
    else:
        return "Português"


def traduzir_interface(self):
    """
    Traduz a interface do usuário com base no idioma atual.
    """
    lang = getattr(self, "current_language", get_system_language)
    t = translations.get(lang, translations["Português"])

    # Nome j<PERSON>, traduzir os widgets
    widgets_para_translate = {
        "btn_incluir_api_key": "Adicionar Chave",
        "btn_consulta": "Buscar Dados",
        "btn_salvar": "Salvar Excel",
        "btn_excluir_api_key": "Excluir Chave",
        "btn_options": "Opções",
    }

    # Traduz os botões
    for attr, key in widgets_para_translate.items():
        widget = getattr(self, attr, None)
        if widget:
            widget.setText(t[key])

    # Traduz o texto de introdução
    if hasattr(self, "label_intro"):
        original_intro = "<p>Bem-vindo ao <b>Pratiko</b>, " \
        "seu assistente de dados da <b>ES Logistics</b>.</p>"
        self.label_intro.setText(t[original_intro])

    # Traduz o texto de status
    if hasattr(self, "label_status") and hasattr(self, "current_status_key"):
        # Se tem uma chave de status armazenada e parâmetros, traduz
        if hasattr(self, "current_status_params") and self.current_status_params:
            self.label_status.setText(t[self.current_status_key].format(**self.current_status_params))
        else:
            self.label_status.setText(t[self.current_status_key])

    # Traduz o widget de filtro de processos
    if hasattr(self, "process_filter"):
        self.process_filter.update_language(lang)

    # Traduz os labels do formulário
    if hasattr(self, "form_inputs"):
        form_labels = [
            ("label_ref_processo", "Referência do Processo ES:"),
            ("label_data_inicial", "Data Inicial (dd/mm/aaaa):"),
            ("label_data_final", "Data Final (dd/mm/aaaa):"),
            ("label_ref_interna", "Referência Cliente:"),
            ("label_num_container", "Número do Container:"),
        ]
        for attr, key in form_labels:
            label = getattr(self.form_inputs, attr, None)
            if label is not None:
                label.setText(t[key])


def translate(key, lang=None):
    """
    Traduz uma chave de texto para o idioma especificado.

    Args:
        key (str): A chave de texto a ser traduzida.
        lang (str, optional): O idioma para o qual traduzir. Se None, usa o idioma atual.

    Returns:
        str: O texto traduzido ou a chave original se não houver tradução disponível.
    """
    if lang is None:
        try:
            lang = _current_language
        except NameError:
            lang = get_system_language()
    t = translations.get(lang, translations["Português"])
    return t.get(key, key)
