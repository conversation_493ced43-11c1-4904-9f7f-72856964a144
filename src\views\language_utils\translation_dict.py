translations = {
    "Português": {
        # Mensagens de boas-vindas e status
        "<p>Bem-vindo ao <b><PERSON>rat<PERSON></b>, seu assistente de dados da <b>ES Logistics</b>.</p>": "<p>Bem-vindo ao <b><PERSON>ratiko</b>, seu assistente de dados da <b>ES Logistics</b>.</p>",
        '<b>Status:</b> API Key "{first_key}" selecionada automaticamente.': '<b>Status:</b> API Key "{first_key}"" selecionada automaticamente.',
        '<b>Status:</b> API Key "{selected_name}" selecionada com sucesso.': '<b>Status:</b> API Key "{selected_name}" selecionada com sucesso.',
        "<b>Status:</b> API Key não encontrada. Selecione uma válida.": "<b>Status:</b> API Key não encontrada. Selecione uma válida.",
        "<b>Status:</b> Nenhuma chave de API disponível.": "<b>Status:</b> Nenhuma chave de API disponível.",
        "<b>Status:</b> Dados obtidos com sucesso!": "<b>Status:</b> Dados obtidos com sucesso!",
        "<b>Status:</b> Falha ao processar os dados.": "<b>Status:</b> Falha ao processar os dados.",
        "<b>Status:</b> Nenhum dado encontrado.": "<b>Status:</b> Nenhum dado encontrado.",
        "O aplicativo continuará rodando em segundo plano.\nClique duas vezes no ícone para reabrir.": "O aplicativo continuará rodando em segundo plano.\nClique duas vezes no ícone para reabrir.",

        # Mensagens de erro
        "Erro": "Erro",
        "Erro ao carregar keys.json: {error}": "Erro ao carregar keys.json: {error}",
        "Erro ao carregar keys.json em get_api_key: {error}": "Erro ao carregar keys.json em get_api_key: {error}",
        "Erro ao exportar dados: {error}": "Erro ao exportar dados: {error}",
        "Erro na requisição: {status_code}": "Erro na requisição: {status_code}",
        "Resposta de erro: {text}": "Resposta de erro: {text}",
        "Exceção ao fazer requisição: {error}": "Exceção ao fazer requisição: {error}",
        "Erro ao verificar coluna {col}: {error}": "Erro ao verificar coluna {col}: {error}",
        "Erro ao carregar colunas selecionadas: {error}": "Erro ao carregar colunas selecionadas: {error}",
        "Erro: O campo 'dados' da API não é uma lista ou dicionário": "Erro: O campo 'dados' da API não é uma lista ou dicionário",
        "Erro: Resposta da API não contém o campo 'dados'": "Erro: Resposta da API não contém o campo 'dados'",
        "Erro: Formato de resposta inválido da API. Tipo: {tipo}": "Erro: Formato de resposta inválido da API. Tipo: {tipo}",

        # Mensagens de log
        "Iniciando exportação para empresa: {company_name}": "Iniciando exportação para empresa: {company_name}",
        "API key recebida: {api_key}": "API key recebida: {api_key}",
        "API key da instância: {api_key}": "API key da instância: {api_key}",
        "Caminho do arquivo keys.json: {path}": "Caminho do arquivo keys.json: {path}",
        "Conteúdo do arquivo keys.json: {keys}": "Conteúdo do arquivo keys.json: {keys}",
        "Carregou API key para empresa {company_name}: {api_key}": "Carregou API key para empresa {company_name}: {api_key}",
        "Empresa {company_name} não encontrada em keys.json": "Empresa {company_name} não encontrada em keys.json",
        "Arquivo keys.json não encontrado em: {path}": "Arquivo keys.json não encontrado em: {path}",
        "Nenhum company_name fornecido, usando API key do parâmetro": "Nenhum company_name fornecido, usando API key do parâmetro",
        "Usando API key da instância como fallback: {api_key}": "Usando API key da instância como fallback: {api_key}",
        "Fazendo requisição para URL: {url}": "Fazendo requisição para URL: {url}",
        "Headers: {headers}": "Headers: {headers}",
        "Status code: {status_code}": "Status code: {status_code}",
        "Resposta recebida: {response}": "Resposta recebida: {response}",
        "Dados encontrados: {count}": "Dados encontrados: {count}",
        "Nenhum dado encontrado na resposta.": "Nenhum dado encontrado na resposta.",
        "Aviso: Nenhum dado encontrado para o período especificado.": "Aviso: Nenhum dado encontrado para o período especificado.",

        # Botões e ações
        "Adicionar Chave": "Adicionar Chave",
        "Buscar Dados": "Buscar Dados",
        "Salvar Excel": "Salvar Excel",
        "Excluir Chave": "Excluir Chave",
        "Opções": "Opções",
        "Configurações": "Configurações",
        "Settings": "Configurações",
        "Incluir API Key": "Incluir API Key",
        "Incluir chave": "Incluir chave",
        "Limpar Tudo": "Limpar Tudo",
        "Selecionar Tudo": "Selecionar Tudo",
        "Mostrar": "Mostrar",
        "Sair": "Sair",
        "Agendar": "Agendar",
        "Salvar": "Salvar",
        "Adicionar": "Adicionar",
        "Remover": "Remover",
        "Testar Notificação": "Testar Notificação",
        "Teste direto de notificação do sistema": "Teste direto de notificação do sistema",
        "Teste direto de notificação do sistema tray": "Teste direto de notificação do sistema tray",
        "Teste de notificação do scheduler": "Teste de notificação do scheduler",
        "Ok": "Ok",
        "OK": "OK",
        "Cancel": "Cancelar",
        "Sim": "Sim",
        "Não": "Não",
        "Clear All": "Limpar Tudo",

        # Campos de formulário
        "Referência do Processo ES:": "Referência do Processo ES:",
        "Data Inicial (dd/mm/aaaa):": "Data Inicial (dd/mm/aaaa):",
        "Data Final (dd/mm/aaaa):": "Data Final (dd/mm/aaaa):",
        "Referência Cliente:": "Referência Cliente:",
        "Número do Container:": "Número do Container:",
        "Selecione a linguagem:": "Selecione a linguagem:",
        "Selecione as colunas para exportar:": "Selecione as colunas para exportar:",
        "Selecione o tipo de processo:": "Selecione o tipo de processo:",
        "Select language:": "Selecione a linguagem:",
        "Select columns to export:": "Selecione as colunas para exportar:",
        "Insira o <b>NOME DA EMPRESA</b>:": "Insira o <b>NOME DA EMPRESA</b>:",
        "Insira a nova <b>CHAVE DE API</b>:": "Insira a nova <b>CHAVE DE API</b>:",

        # Opções de filtro
        "Todos os Processos": "Todos os Processos",
        "Processos em Andamento": "Processos em Andamento",
        "Processos Finalizados": "Processos Finalizados",

        # Mensagens de aviso
        "Atenção": "Atenção",
        "As seguintes colunas não foram encontradas: {colunas}": "As seguintes colunas não foram encontradas: {colunas}",
        "Insira uma chave de API para continuar.": "Insira uma chave de API para continuar.",
        "Nenhuma chave de API selecionada.": "Nenhuma chave de API selecionada.",
        "Arquivo da logo do Pratiko não encontrado.": "Arquivo da logo do Pratiko não encontrado.",
        "Arquivo da logo da empresa não encontrado.": "Arquivo da logo da empresa não encontrado.",
        "Nome já existe. Insira um <b>nome</b> diferente.": "Nome já existe. Insira um <b>nome</b> diferente.",
        "API Key já existe. Insira uma <b>chave diferente</b>.": "API Key já existe. Insira uma <b>chave diferente</b>.",
        "<b>A API Key deve ter pelo menos 32 caracteres</b>.": "<b>A API Key deve ter pelo menos 32 caracteres</b>.",
        "Arquivo de chaves de API não encontrado.": "Arquivo de chaves de API não encontrado.",
        "Arquivo de chaves de API inválido.": "Arquivo de chaves de API inválido.",
        "Chave de API nao encontrada.": "Chave de API nao encontrada.",
        "Preencha pelo menos um dos campos obrigatórios.": "Preencha pelo menos um dos campos obrigatórios.",
        "Data inicial inválida! Use o formato dd/mm/aaaa.": "Data inicial inválida! Use o formato dd/mm/aaaa.",
        "Data inicial deve ser menor que a data final.": "Data inicial deve ser menor que a data final.",
        "O intervalo de datas deve ser menor que 30 dias.": "O intervalo de datas deve ser menor que 30 dias.",
        "Selecione uma chave de API antes de consultar.": "Selecione uma chave de API antes de consultar.",
        "Nenhum dado encontrado.": "Nenhum dado encontrado.",
        "Nenhum dado para salvar.": "Nenhum dado para salvar.",
        "Data inicial inválida.": "Data inicial inválida.",
        "Data final inválida.": "Data final inválida.",
        "Nenhuma das colunas selecionadas existe nos dados.": "Nenhuma das colunas selecionadas existe nos dados.",
        "Nenhum dado para exibir na tabela.": "Nenhum dado para exibir na tabela.",

        # Mensagens de erro adicionais
        "Erro ao excluir a chave de API.": "Erro ao excluir a chave de API.",
        "<b>Erro</b> ao salvar chave de API.": "<b>Erro</b> ao salvar chave de API.",
        "Erro ao ler o arquivo de chaves de API.": "Erro ao ler o arquivo de chaves de API.",
        "Erro ao salvar o arquivo de chaves de API.": "Erro ao salvar o arquivo de chaves de API.",
        "Erro ao buscar dados, tente novamente.": "Erro ao buscar dados, tente novamente.",
        "Erro ao carregar as API Keys: {e}": "Erro ao carregar as API Keys: {e}",
        "Erro ao salvar as API Keys: {e}": "Erro ao salvar as API Keys: {e}",
        "Erro ao salvar o arquivo Excel: {error}": "Erro ao salvar o arquivo Excel: {error}",
        "Ocorreu um erro ao exibir a mensagem: {e}": "Ocorreu um erro ao exibir a mensagem: {e}",

        # Mensagens de sucesso
        "Informação": "Informação",
        "Chave(s) adicionada(s) com <b>sucesso</b>.": "Chave(s) adicionada(s) com <b>sucesso</b>.",
        "Chave de API excluída com sucesso.": "Chave de API excluída com sucesso.",
        "Arquivo Excel salvo em {filename}": "Arquivo Excel salvo em {filename}",

        # Mensagens de confirmação
        "Deseja adicionar outra chave de API?": "Deseja adicionar outra chave de API?",
        "Tem certeza de que deseja excluir a chave de API \"{selected_name}\"?": "Tem certeza de que deseja excluir a chave de API \"{selected_name}\"?",

        # Mensagens técnicas
        'O campo "dados" não é uma lista ou dicionário. Tipo recebido: {tipo}': 'O campo "dados" não é uma lista ou dicionário. Tipo recebido: {tipo}',

        # Idiomas
        "Português": "Português",
        "English": "English",

        # Colunas de dados
        "Ref. cliente": "Ref. cliente",
        "Ref. Sistema": "Ref. Sistema",
        "Master": "Master",
        "House": "House",
        "Solicitação de Booking": "Solicitação de Booking",
        "Confirmação de Booking": "Confirmação de Booking",
        "Prontidao de carga": "Prontidao de carga",
        "Fornecedor de Coleta": "Fornecedor de Coleta",
        "Previsão de Coleta": "Previsão de Coleta",
        "Confirmação de Coleta": "Confirmação de Coleta",
        "Shipper": "Shipper",
        "Navio": "Navio",
        "Viagem": "Viagem",
        "Carrier": "Carrier",
        "Porto de Origem": "Porto de Origem",
        "Previsão de saída": "Previsão de saída",
        "Confirmação de embarque": "Confirmação de embarque",
        "Porto de Destino": "Porto de Destino",
        "Previsão de chegada": "Previsão de chegada",
        "Confirmação de chegada": "Confirmação de chegada",
        "Incoterm": "Incoterm",
        "Consignee": "Consignee",
        "Notify": "Notify",
        "Purchase Order": "Purchase Order",
        "Invoice": "Invoice",
        "Fornecedor de Entrega": "Fornecedor de Entrega",
        "Previsão de Entrega": "Previsão de Entrega",
        "Confirmação de Entrega": "Confirmação de Entrega",
        "Volume Containers": "Volume Containers",
        "Transbordo": "Transbordo",
        "Containers": "Containers",

        # Mensagens de sucesso adicionais
        "Opções salvas com sucesso.": "Opções salvas com sucesso.",
        "Foram encontrados {count} processos em andamento.": "Foram encontrados {count} processos em andamento.",
        "Foram encontrados {count} processos finalizados.": "Foram encontrados {count} processos finalizados.",

        # Mensagens de aviso adicionais
        "Selecione pelo menos uma coluna.": "Selecione pelo menos uma coluna.",
        "Nenhum processo em andamento encontrado.": "Nenhum processo em andamento encontrado.",
        "Nenhum dado encontrado com o filtro selecionado.": "Nenhum dado encontrado com o filtro selecionado.",
        "Nenhum processo finalizado encontrado.": "Nenhum processo finalizado encontrado.",

        # Elementos do Agendador
        "Agendador Automático": "Agendador Automático",
        "Horários de Execução": "Horários de Execução",
        "Horários de Execução:": "Horários de Execução:",
        "Adicionar Horários": "Adicionar Horários",
        "Adicionar Horários:": "Adicionar Horários:",
        "Dias da Semana:": "Dias da Semana:",
        "Segunda": "Segunda",
        "Terça": "Terça",
        "Quarta": "Quarta",
        "Quinta": "Quinta",
        "Sexta": "Sexta",
        "Sábado": "Sábado",
        "Domingo": "Domingo",

        # Mensagens de erro do agendador
        "Arquivo do agendador não encontrado.": "Arquivo do agendador não encontrado.",
        "Falha ao agendar exportação para {hour}:{minute}": "Falha ao agendar exportação para {hour}:{minute}",
        "Falha na exportação.": "Falha na exportação.",
        "Falha na exportação agendada {e}.": "Falha na exportação agendada {e}.",
        "Falha na exportação agendada: {e}": "Falha na exportação agendada: {e}",
        "Erro ao salvar agendamento: {e}": "Erro ao salvar agendamento: {e}",
        "Erro ao carregar a configuração do agendador.": "Erro ao carregar a configuração do agendador.",
        "Erro ao carregar API key do arquivo de configuração.": "Erro ao carregar API key do arquivo de configuração.",
        "Erro: Formato de data inválido. Use dd/mm/aaaa ou yyyy-mm-dd": "Erro: Formato de data inválido. Use dd/mm/aaaa ou yyyy-mm-dd",
        "Erro: API key não fornecida e não encontrada no arquivo de configuração": "Erro: API key não fornecida e não encontrada no arquivo de configuração",
        "Erro: Não foi possível obter ou salvar os dados": "Erro: Não foi possível obter ou salvar os dados",
        "Erro ao carregar a configuração do agendador: {e}": "Erro ao carregar a configuração do agendador: {e}",
        "Erro ao salvar configuração: {error}": "Erro ao salvar configuração: {error}",
        "Exportação não pode ser realizada com sucesso.": "Exportação não pode ser realizada com sucesso.",
        "Erro ao excluir o agendamento.": "Erro ao excluir o agendamento.",

        # Mensagens de sucesso do agendador
        "Exportação realizada com sucesso.": "Exportação realizada com sucesso.",
        "Arquivo salvo com sucesso em: {filepath}": "Arquivo salvo com sucesso em: {filepath}",
        "Agendamento excluído com sucesso.": "Agendamento excluído com sucesso.",
        "Agendamento salvo com sucesso.": "Agendamento salvo com sucesso.",

        "Selecione pelo menos um dia da semana.": "Selecione pelo menos um dia da semana.",
        "Todos os agendamentos foram desativados.": "Todos os agendamentos foram desativados.",

        # Notificações
        "Testar Notificação": "Testar Notificação",
        "Teste direto de notificação do sistema": "Teste direto de notificação do sistema",
        "Teste direto de notificação do sistema tray": "Teste direto de notificação do sistema tray",
        "Notificação enviada para a bandeja do sistema.": "Notificação enviada para a bandeja do sistema.",
        "Ícone da bandeja do sistema não disponível.": "Ícone da bandeja do sistema não disponível.",
        "Teste de notificação do scheduler": "Teste de notificação do scheduler",

        # Mensagens de confirmação do agendador
        'Tem certeza de que deseja excluir o agendamento para "{company}"?': 'Tem certeza de que deseja excluir o agendamento para "{company}"?',
        "Operação Cancelada.": "Operação Cancelada.",

        # Elementos do SchedulerListDialog
        "Gerenciador de Agendamentos": "Gerenciador de Agendamentos",
        "Novo Agendamento": "Novo Agendamento",
        "Editar Agendamento": "Editar Agendamento",
        "Agendamentos Configurados": "Agendamentos Configurados",
        "Agendamentos Existentes": "Agendamentos Existentes",
        "Agendamentos": "Agendamentos",
        "Nenhum": "Nenhum",
        "Empresas": "Empresas",
        "Empresas:": "Empresas:",
        "Empresa:": "Empresa:",
        "Empresa": "Empresa",
        "Horários": "Horários",
        "Dias": "Dias",
        "Ações": "Ações",
        "Editar": "Editar",
        "Excluir": "Excluir",
        "Adicionar Novo": "Adicionar Novo",
        "Seg": "Seg",
        "Ter": "Ter",
        "Qua": "Qua",
        "Qui": "Qui",
        "Sex": "Sex",
        "Sáb": "Sáb",
        "Dom": "Dom",
    },

    "English": {
        # Mensagens de boas-vindas e status
        "<p>Bem-vindo ao <b>Pratiko</b>, seu assistente de dados da <b>ES Logistics</b>.</p>": "<p>Welcome to <b>Pratiko</b>, the data assistant by <b>ES Logistics</b>.</p>",
        '<b>Status:</b> API Key "{first_key}" selecionada automaticamente.': '<b>Status:</b> API Key "{first_key}" automatically selected.',
        '<b>Status:</b> API Key "{selected_name}" selecionada com sucesso.': '<b>Status:</b> API Key "{selected_name}" selected successfully.',
        "<b>Status:</b> API Key não encontrada. Selecione uma válida.": "<b>Status:</b> API Key not found. Please select a valid one.",
        "<b>Status:</b> Nenhuma chave de API disponível.": "<b>Status:</b> No API key available.",
        "<b>Status:</b> Dados obtidos com sucesso!": "<b>Status:</b> Data fetched successfully!",
        "<b>Status:</b> Falha ao processar os dados.": "<b>Status:</b> Failed to process data.",
        "<b>Status:</b> Nenhum dado encontrado.": "<b>Status:</b> No data found.",
        "O aplicativo continuará rodando em segundo plano.\nClique duas vezes no ícone para reabrir.": "The application will continue running in the background.\nDouble-click the icon to reopen.",

        # Mensagens de erro
        "Erro": "Error",
        "Erro ao carregar keys.json: {error}": "Error loading keys.json: {error}",
        "Erro ao carregar keys.json em get_api_key: {error}": "Error loading keys.json in get_api_key: {error}",
        "Erro ao exportar dados: {error}": "Error exporting data: {error}",
        "Erro na requisição: {status_code}": "Error in request: {status_code}",
        "Resposta de erro: {text}": "Error response: {text}",
        "Exceção ao fazer requisição: {error}": "Exception when making request: {error}",
        "Erro ao verificar coluna {col}: {error}": "Error checking column {col}: {error}",
        "Erro ao carregar colunas selecionadas: {error}": "Error loading selected columns: {error}",
        "Erro: O campo 'dados' da API não é uma lista ou dicionário": "Error: The API 'data' field is not a list or dictionary",
        "Erro: Resposta da API não contém o campo 'dados'": "Error: API response does not contain the 'data' field",
        "Erro: Formato de resposta inválido da API. Tipo: {tipo}": "Error: Invalid API response format. Type: {tipo}",

        # Mensagens de log
        "Iniciando exportação para empresa: {company_name}": "Starting export for company: {company_name}",
        "API key recebida: {api_key}": "API key received: {api_key}",
        "API key da instância: {api_key}": "Instance API key: {api_key}",
        "Caminho do arquivo keys.json: {path}": "Path to keys.json file: {path}",
        "Conteúdo do arquivo keys.json: {keys}": "Content of keys.json file: {keys}",
        "Carregou API key para empresa {company_name}: {api_key}": "Loaded API key for company {company_name}: {api_key}",
        "Empresa {company_name} não encontrada em keys.json": "Company {company_name} not found in keys.json",
        "Arquivo keys.json não encontrado em: {path}": "keys.json file not found at: {path}",
        "Nenhum company_name fornecido, usando API key do parâmetro": "No company_name provided, using API key from parameter",
        "Usando API key da instância como fallback: {api_key}": "Using instance API key as fallback: {api_key}",
        "Fazendo requisição para URL: {url}": "Making request to URL: {url}",
        "Headers: {headers}": "Headers: {headers}",
        "Status code: {status_code}": "Status code: {status_code}",
        "Resposta recebida: {response}": "Response received: {response}",
        "Dados encontrados: {count}": "Data found: {count}",
        "Nenhum dado encontrado na resposta.": "No data found in response.",
        "Aviso: Nenhum dado encontrado para o período especificado.": "Warning: No data found for the specified period.",

        # Botões e ações
        "Adicionar Chave": "Add Key",
        "Buscar Dados": "Search",
        "Salvar Excel": "Save Excel",
        "Excluir Chave": "Delete Key",
        "Opções": "Options",
        "Configurações": "Settings",
        "Settings": "Settings",
        "Incluir API Key": "Add API Key",
        "Incluir chave": "Add key",
        "Limpar Tudo": "Clear All",
        "Selecionar Tudo": "Select All",
        "Mostrar": "Show",
        "Sair": "Exit",
        "Agendar": "Schedule",
        "Salvar": "Save",
        "Adicionar": "Add",
        "Remover": "Remove",
        "Testar Notificação": "Test Notification",
        "Teste direto de notificação do sistema": "Direct system notification test",
        "Teste direto de notificação do sistema tray": "Direct system tray notification test",
        "Teste de notificação do scheduler": "Scheduler notification test",
        "Ok": "Ok",
        "OK": "OK",
        "Cancel": "Cancel",
        "Sim": "Yes",
        "Não": "No",
        "Clear All": "Clear All",

        # Campos de formulário
        "Referência do Processo ES:": "ES Process Reference:",
        "Data Inicial (dd/mm/aaaa):": "Start Date (dd/mm/yyyy):",
        "Data Final (dd/mm/aaaa):": "End Date (dd/mm/yyyy):",
        "Referência Cliente:": "Internal Reference:",
        "Número do Container:": "Container Number:",
        "Selecione a linguagem:": "Select language:",
        "Selecione as colunas para exportar:": "Select columns to export:",
        "Selecione o tipo de processo:": "Select process type:",
        "Insira o <b>NOME DA EMPRESA</b>:": "Enter the <b>COMPANY NAME</b>:",
        "Insira a nova <b>CHAVE DE API</b>:": "Enter the new <b>API KEY</b>:",

        # Opções de filtro
        "Todos os Processos": "All Processes",
        "Processos em Andamento": "Ongoing",
        "Processos Finalizados": "Finished",

        # Mensagens de aviso
        "Atenção": "Notice",
        "As seguintes colunas não foram encontradas: {colunas}": "The following columns were not found: {colunas}",
        "Insira uma chave de API para continuar.": "Enter an API key to continue.",
        "Nenhuma chave de API selecionada.": "No API key selected.",
        "Arquivo da logo do Pratiko não encontrado.": "Pratiko logo file not found.",
        "Arquivo da logo da empresa não encontrado.": "Company logo file not found.",
        "Nome já existe. Insira um <b>nome</b> diferente.": "Name already exists. Enter a <b>different name</b>.",
        "API Key já existe. Insira uma <b>chave diferente</b>.": "API Key already exists. Enter a <b>different key</b>.",
        "<b>A API Key deve ter pelo menos 32 caracteres</b>.": "<b>The API Key must have at least 32 characters</b>.",
        "Arquivo de chaves de API não encontrado.": "API key file not found.",
        "Arquivo de chaves de API inválido.": "Invalid API key file.",
        "Chave de API nao encontrada.": "API key not found.",
        "Preencha pelo menos um dos campos obrigatórios.": "Fill in at least one of the required fields.",
        "Data inicial inválida! Use o formato dd/mm/aaaa.": "Invalid start date! Use the format dd/mm/yyyy.",
        "Data inicial deve ser menor que a data final.": "Start date must be earlier than end date.",
        "O intervalo de datas deve ser menor que 30 dias.": "The date range must be less than 30 days.",
        "Selecione uma chave de API antes de consultar.": "Select an API key before querying.",
        "Nenhum dado encontrado.": "No data found.",
        "Nenhum dado para salvar.": "No data to save.",
        "Data inicial inválida.": "Invalid start date.",
        "Data final inválida.": "Invalid end date.",
        "Nenhuma das colunas selecionadas existe nos dados.": "None of the selected columns exist in the data.",
        "Nenhum dado para exibir na tabela.": "No data to display in the table.",

        # Mensagens de erro adicionais
        "Erro ao excluir a chave de API.": "Error deleting API key.",
        "<b>Erro</b> ao salvar chave de API.": "<b>Error</b> saving API key.",
        "Erro ao ler o arquivo de chaves de API.": "Error reading API key file.",
        "Erro ao salvar o arquivo de chaves de API.": "Error saving API key file.",
        "Erro ao buscar dados, tente novamente.": "Error fetching data, please try again.",
        "Erro ao carregar as API Keys: {e}": "Error loading API Keys: {e}",
        "Erro ao salvar as API Keys: {e}": "Error saving API Keys: {e}",
        "Erro ao salvar o arquivo Excel: {error}": "Error saving Excel file: {error}",
        "Ocorreu um erro ao exibir a mensagem: {e}": "An error occurred while displaying the message: {e}",

        # Mensagens de sucesso
        "Informação": "Information",
        "Chave(s) adicionada(s) com <b>sucesso</b>.": "Key(s) added <b>successfully</b>.",
        "Chave de API excluída com sucesso.": "API key deleted successfully.",
        "Arquivo Excel salvo em {filename}": "Excel file saved at {filename}",

        # Mensagens de confirmação
        "Deseja adicionar outra chave de API?": "Do you want to add another API key?",
        "Tem certeza de que deseja excluir a chave de API \"{selected_name}\"?": "Are you sure you want to delete the API key \"{selected_name}\"?",

        # Mensagens técnicas
        'O campo "dados" não é uma lista ou dicionário. Tipo recebido: {tipo}': 'The field "data" is not a list or dictionary. Received type: {tipo}',

        # Idiomas
        "Português": "Portuguese",
        "English": "English",

        # Colunas de dados
        "Ref. cliente": "Internal Reference",
        "Ref. Sistema": "System Reference",
        "Master": "Master",
        "House": "House",
        "Solicitação de Booking": "Booking Request",
        "Confirmação de Booking": "Booking Confirmation",
        "Prontidao de carga": "Cargo Readiness",
        "Fornecedor de Coleta": "Collection Supplier",
        "Previsão de Coleta": "Collection Forecast",
        "Confirmação de Coleta": "Collection Confirmation",
        "Shipper": "Shipper",
        "Navio": "Vessel",
        "Viagem": "Voyage",
        "Carrier": "Carrier",
        "Porto de Origem": "Port of Origin",
        "Previsão de saída": "Departure Forecast",
        "Confirmação de embarque": "Shipping Confirmation",
        "Porto de Destino": "Destination Port",
        "Previsão de chegada": "Arrival Forecast",
        "Confirmação de chegada": "Arrival Confirmation",
        "Incoterm": "Incoterm",
        "Consignee": "Consignee",
        "Notify": "Notify",
        "Purchase Order": "Purchase Order",
        "Invoice": "Invoice",
        "Fornecedor de Entrega": "Delivery Supplier",
        "Previsão de Entrega": "Delivery Forecast",
        "Confirmação de Entrega": "Delivery Confirmation",
        "Volume Containers": "Container Volume",
        "Transbordo": "Transshipment",
        "Containers": "Containers",

        # Mensagens de sucesso adicionais
        "Opções salvas com sucesso.": "Options saved successfully.",
        "Foram encontrados {count} processos em andamento.": "{count} processes in progress found.",
        "Foram encontrados {count} processos finalizados.": "{count} completed processes found.",

        # Mensagens de aviso adicionais
        "Selecione pelo menos uma coluna.": "Select at least one column.",
        "Nenhum processo em andamento encontrado.": "No processes in progress found.",
        "Nenhum dado encontrado com o filtro selecionado.": "No data found with the selected filter.",
        "Nenhum processo finalizado encontrado.": "No completed processes found.",

        # Mensagens de erro do agendador
        "Arquivo do agendador não encontrado.": "Scheduler file not found.",
        "Falha ao agendar exportação para {hour}:{minute}": "Failed to schedule export for {hour}:{minute}",
        "Falha na exportação.": "Export failed.",
        "Falha na exportação agendada {e}.": "Failed to schedule export {e}.",
        "Falha na exportação agendada: {e}": "Export scheduling failed: {e}",
        "Erro ao salvar agendamento: {e}": "Error saving schedule: {e}",
        "Erro ao carregar a configuração do agendador.": "Error loading scheduler configuration.",
        "Erro ao carregar API key do arquivo de configuração.": "Error loading API key from configuration file.",
        "Erro: Formato de data inválido. Use dd/mm/aaaa ou yyyy-mm-dd": "Error: Invalid date format. Use dd/mm/yyyy or yyyy-mm-dd",
        "Erro: API key não fornecida e não encontrada no arquivo de configuração": "Error: API key not provided and not found in configuration file",
        "Erro: Não foi possível obter ou salvar os dados": "Error: It was not possible to obtain or save data",
        "Erro ao carregar a configuração do agendador: {e}": "Error loading scheduler configuration: {e}",
        "Erro ao salvar configuração: {error}": "Error saving configuration: {error}",
        "Exportação não pode ser realizada com sucesso.": "Export cannot be completed successfully.",
        "Erro ao excluir o agendamento.": "Error deleting the schedule.",

        # Mensagens de sucesso do agendador
        "Exportação realizada com sucesso.": "Export completed successfully.",
        "Arquivo salvo com sucesso em: {filepath}": "File saved successfully at: {filepath}",
        "Agendamento excluído com sucesso.": "Schedule deleted successfully.",
        "Agendamento salvo com sucesso.": "Schedule saved successfully!",

        # Configurações do agendador
        "Agendador Automático": "Scheduler",
        "Horários de Execução:": "Execution hours:",
        "Adicionar Horários:": "Add Hours:",
        "Dias da Semana:": "Days of the Week:",
        "Segunda": "Monday",
        "Terça": "Tuesday",
        "Quarta": "Wednesday",
        "Quinta": "Thursday",
        "Sexta": "Friday",
        "Sábado": "Saturday",
        "Domingo": "Sunday",
        "Selecione pelo menos um dia da semana.": "Select at least one weekday.",
        "Todos os agendamentos foram desativados.": "All schedules have been disabled.",

        # Notificações
        "Testar Notificação": "Test Notification",
        "Teste direto de notificação do sistema": "Direct system notification test",
        "Teste direto de notificação do sistema tray": "Direct system tray notification test",
        "Notificação enviada para a bandeja do sistema.": "Notification sent to the system tray.",
        "Ícone da bandeja do sistema não disponível.": "System tray icon not available.",
        "Teste de notificação do scheduler": "Scheduler notification test",

        # Mensagens de confirmação do agendador
        'Tem certeza de que deseja excluir o agendamento para "{company}"?': 'Are you sure you want to delete the schedule for "{company}"?',
        "Operação Cancelada.": "Operation Cancelled.",

        # Elementos do SchedulerListDialog
        "Gerenciador de Agendamentos": "Schedule Manager",
        "Agendamentos Configurados": "Configured Schedules",
        "Agendamentos Existentes": "Existing Schedules",
        "Novo Agendamento": "New Schedule",
        "Editar Agendamento": "Edit Schedule",
        "Agendamentos": "Schedules",
        "Nenhum": "None",
        "Empresas": "Companies",
        "Empresas:": "Companies:",
        "Empresa:": "Company:",
        "Empresa": "Company",
        "Horários": "Times",
        "Dias": "Days",
        "Ações": "Actions",
        "Editar": "Edit",
        "Excluir": "Delete",
        "Adicionar Novo": "Add New",
        "Seg": "Mon",
        "Ter": "Tue",
        "Qua": "Wed",
        "Qui": "Thu",
        "Sex": "Fri",
        "Sáb": "Sat",
        "Dom": "Sun",
    }
}