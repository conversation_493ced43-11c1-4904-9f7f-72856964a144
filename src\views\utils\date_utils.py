from datetime import date


def formatar_data(texto, sender):
    """
    Formata a data no formato "dd/mm/aaaa" enquanto o usuário digita.
    """
    cursor_pos = sender.cursorPosition()
    texto = texto.replace("/", "")

    if len(texto) > 8:
        texto = texto[:8]

    if len(texto) > 2:
        texto = texto[:2] + "/" + texto[2:]
    if len(texto) > 5:
        texto = texto[:5] + "/" + texto[5:]

    sender.setText(texto)

    if cursor_pos > 0 and len(texto) > cursor_pos - 1 and texto[cursor_pos - 1] == "/":
        cursor_pos += 1
    sender.setCursorPosition(cursor_pos)


def dias_entre_datas(data1, data2):
    """
    Calcula o número de dias entre duas datas no formato "dd/mm/aaaa".
    Usado em functions.py para validar o intervalo de datas antes de fazer a consulta.
    """
    d1, m1, a1 = map(int, data1.split("/"))
    d2, m2, a2 = map(int, data2.split("/"))

    dt1 = date(a1, m1, d1)
    dt2 = date(a2, m2, d2)
    return (dt2 - dt1).days