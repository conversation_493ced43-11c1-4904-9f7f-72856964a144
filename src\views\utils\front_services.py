from PySide6.QtWidgets import (
    QMessageBox, 
    QApplication,
    QSystemTrayIcon
)

from services.language_utils.translations_functions import translate as t
from views.utils.widget_style import WidgetStyle


# Estilo para a caixa de mensagem padronizada
message_box_style = (WidgetStyle().message_box())


def exibir_erro(parent, mensagem):
    """
    Exibe uma caixa de mensagem de erro.
    """
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(t("Erro"))
    msg_box.setWindowIcon(QApplication.instance().windowIcon())
    msg_box.setText(mensagem)
    msg_box.setStyleSheet(message_box_style)
    msg_box.exec()


def exibir_information(parent, mensagem):
    """
    Exibe uma caixa de mensagem de informação.
    """
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Information)
    msg_box.setWindowTitle(t("Informação"))
    msg_box.setWindowIcon(QApplication.instance().windowIcon())
    msg_box.setText(mensagem)
    msg_box.setStyleSheet(message_box_style)
    msg_box.exec()


def exibir_question(parent, mensagem):
    """
    Exibe uma caixa de mensagem de pergunta e retorna a resposta.
    """
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle(t("Atenção"))
    msg_box.setWindowIcon(QApplication.instance().windowIcon())
    msg_box.setText(mensagem)

    # Setando os botões
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    yes_button = msg_box.button(QMessageBox.Yes)
    yes_button.setText(t("Sim"))
    no_button = msg_box.button(QMessageBox.No)
    no_button.setText(t("Não"))

    msg_box.setStyleSheet(message_box_style)
    try:
        return msg_box.exec()
    except Exception as e:
        exibir_erro(parent, t(f"Ocorreu um erro ao exibir a mensagem: {e}"))
        return QMessageBox.No

@staticmethod
def user_checks():
    return (
        "Todos os Processos",
        "Processos em Andamento",
        "Processos Finalizados",
    )

def notify(self, title, message, icon_type="info", duration=3000):

    if self.tray_icon:
        icon = QSystemTrayIcon.Information if icon_type == "info" else QSystemTrayIcon.Warning
        self.tray_icon.showMessage(title, message, icon, duration)

