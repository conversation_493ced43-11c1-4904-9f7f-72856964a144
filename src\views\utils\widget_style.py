class WidgetStyle:
    """
    Classe para armarzenar os estilos CSS dos widgets.
    """

    def __init__(self):
        self.buttons_default = """
            QPushButton {
                background-color: #2a5a8a;
                border: none;
                border-radius: 15px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a6a9a;
            }
        """

        # Estilo do botão de fechar
        self.button_close = """
            QPushButton {
                background-color: #ff5555;
                border: none;
                border-radius: 15px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ff7777;
            }
        """

        # Estilo do background da janela principal
        self.style_sheet = """
            QWidget {
                background-color: rgba(22, 53, 91, 1);
                color: white;
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit {
                background-color: rgba(255, 255, 255, 0.2);
                border: 1px solid #2a5a8a;
                border-radius: 5px;
                padding: 0px;
                color: white;
                min-width: 400px;
                font-size: 12px;
            }
            QPushButton {
                background-color: #2a5a8a;
                border: none;
                border-radius: 5px;
                padding: 10px;
                color: white;
            }
            QPushButton:hover {
                background-color: #3a6a9a;
            }
            QGroupBox {
                border: 1px solid #2a5a8a;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 5px;

            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
            QTableWidget {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid #2a5a8a;
                border-radius: 5px;
                color: white;
            }
            QHeaderView::section {
                background-color: #2a5a8a;
                color: white;
                padding: 0px;
                border: none;
            }
            QMessageBox QLabel {
                color: black;
            }
        """

    # Function para mudar o background da caixa de options
    def background_style(self):
        """
        Retorna o estilo CSS para o fundo da caixa de opções.
        """
        return (
            """
            QDialog {
                background-color: #16355B;  /* Dark blue background */
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            QComboBox, QListWidget {
                background-color: #16355B;  /* Same as dialog background */
                color: white;
                border: 1px solid #2a5a8a;
                border-radius: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #2F4F74;  /* Dropdown background */
                color: white;
                border: 1px solid #2F4F74;
                border-radius: 5px;
            }
            QListWidget::item {
                color: white;
            }
            QListWidget::item:hover {
                background-color: #2a5a8a;
            }
            QDialogButtonBox QPushButton {
                background-color: #1E90FF;  /* Light blue buttons */
                color: white;
                font-size: 12px;
                border: none;
                padding: 6px 18px;
                border-radius: 5px;
            }
            QDialogButtonBox QPushButton:hover {
                background-color: #4682B4;
            }
            """
        )

    def message_box(self):
        return (
            """
            QMessageBox {
                background-color: #16355B;  /* Fundo azul escuro */
                color: white;  /* Texto branco */
            }
            QLabel {
                color: white;  /* Texto branco */
                font-size: 14px;  /* Tamanho da fonte */
            }
            QPushButton {
                background-color: #1E90FF;  /* Botão azul claro */
                color: white;  /* Texto do botão branco */
                font-size: 12px;  /* Tamanho da fonte do botão */
                border: none;
            }
            QPushButton:hover {
                background-color: #4682B4;  /* Azul mais claro ao passar o mouse */
            }
            """
        )

    @staticmethod
    def remove_button_style():
        return (
            """
                QPushButton {
                    color: #FF5555;
                    border: 1px solid #FF5555;
                    border-radius: 2px;
                    background: transparent;
                    padding: 0px;
                }
                QPushButton:hover {
                    color: white;
                    background: #FF5555;
                }
            """
        )


    @staticmethod
    def table_style():
        return (
            """
            QTableWidget {
                background-color: #16355B;
                color: white;
                border: 1px solid #2a5a8a;
                border-radius: 5px;
                gridline-color: #2a5a8a;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2a5a8a;
                border-right: 1px solid #2a5a8a;
            }
            QTableWidget::item:selected {
                background-color: #2F4F74;
            }
            QHeaderView::section {
                background-color: #2F4F74;
                color: white;
                padding: 5px;
                border: 1px solid #2a5a8a;
                font-weight: bold;
                text-align: center;
            }
            QTableWidget QTableWidgetItem {
                text-align: center;
            }
            """
        )


    @staticmethod
    def left_container_style():
        return (
            """
            #borderedWidget {
                background-color: #16355B;
                border: 1px solid #2a5a8a;
                border-radius: 10px;
            }
            """
        )

    @staticmethod
    def right_container_style():
        return (
            """
            #borderedWidget {
                background-color: #16355B;
                border: 1px solid #2a5a8a;
                border-radius: 10px;
            }
            """
        )

    @staticmethod
    def edit_button_style():
        return (
            """
                QPushButton {
                    background-color: #2a5a8a;
                    border: none;
                    border-radius: 5px;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3a6a9a;
                }
            """
        )

    @staticmethod
    def delete_button_style():
        return (
            """
                QPushButton {
                    background-color: #2a5a8a;
                    border: none;
                    border-radius: 5px;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3a6a9a;
                }
            """
        )

    @staticmethod
    def edit_time_style():
        return (
            """
            QTimeEdit {
                background-color: #2F4F74;
                color: white;
                border: 1px solid #2a5a8a;
                border-radius: 3px;
                padding: 2px;
                font-size: 12px;
            }
            """
        )

    @staticmethod
    def add_button_style():
        return (
            """
            QPushButton {
                background-color: #2F4F74;
                color: white;
                border: 2px solid #2a5a8a;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
                min-width: 20px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #3a6a9a;
            }
            QPushButton:disabled {
                background-color: #1A2A3A;
                color: #808080;
            }
            """
        )

    @staticmethod
    def radio_style():
        return (
            """
            QRadioButton {
                color: white;
                font-size: 12px;
                padding: 2px 2px;
                margin-right: 5px;
                min-width: 50px;
                border-radius: 5px;
            }
            QRadioButton:hover {
                background-color: rgba(42, 90, 138, 0.3);
            }
            QRadioButton:pressed {
                background-color: rgba(42, 90, 138, 0.5);
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                margin-right: 2px;
            }
            QRadioButton::indicator:hover {
                border-color: #3a7ab5;
            }
            QRadioButton::indicator:unchecked {
                background-color: #16355B;
                border: 1.5px solid white;
            }
            QRadioButton::indicator:unchecked:hover {
                border: 2px solid #3a7ab5;
            }
            QRadioButton::indicator:checked {
                background-color: white;
                border: 1.5px solid white;
            }
            QRadioButton::indicator:checked:hover {
                background-color: #f0f0f0;
            }
        """
        )


    @staticmethod
    def api_key_style():
        return (
            """
            QComboBox {
                background-color: #16355B;
                color: white;
                border: 1px solid #2a5a8a;
                border-radius: 5px;
                padding: 2px 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #2F4F74;
                color: white;
                border: 1px solid #2a5a8a;
                border-radius: 5px;
            }
        """
        )
