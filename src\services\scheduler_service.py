import json
import os
import threading
from datetime import datetime, timed<PERSON>ta

from PySide6.QtWidgets import QSystemTrayIcon
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

from controllers.cli_controller import CLIController
from services.language_utils.translations_functions import translate as t
from views.utils.front_services import exibir_erro
from views.system_tray import SystemTrayIcon

class SchedulerService:
    """
    Serviço para gerenciar as configurações do agendador
    """

    # Lock global para sincronizar exportações
    _global_export_lock = threading.Lock()

    # Dict de classe para rastrear exportações por empresa
    _company_exports = {}

    def __init__(self, api_key=None, tray_icon=None, main_window=None):
        """
        Inicializa o serviço de agendamento de search e export
        """
        self.config_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            'config',
            'scheduler_config.json'
        )
        # Tenta encontrar o arquivo keys.json em vários locais possíveis
        try:
            import sys

            # Lista de possíveis locais para o arquivo keys.json
            possible_locations = []

            # 1. No diretório do executável (quando empacotado)
            if hasattr(sys, "_MEIPASS"):
                possible_locations.append(os.path.join(os.path.dirname(sys.executable), 'keys.json'))

            # 2. No diretório raiz do projeto
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            possible_locations.append(os.path.join(project_root, 'keys.json'))

            # 3. No diretório src
            possible_locations.append(os.path.join(project_root, 'src', 'keys.json'))

            # 4. No mesmo diretório que o módulo
            possible_locations.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'keys.json'))

            # Verifica cada local e usa o primeiro que existir
            for location in possible_locations:
                if os.path.exists(location):
                    self.keys_path = location
                    break
            else:
                # Se nenhum for encontrado, usa o padrão no diretório src
                self.keys_path = os.path.join(project_root, 'src', 'keys.json')

        except Exception:
            # Fallback para o diretório src
            self.keys_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'src',
                'keys.json'
            )
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        self.selected_company = None
        self.api_key = api_key
        self.tray_icon = tray_icon
        self.main_window = main_window
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()
        self._apply_saved_schedule()

    def set_api_key(self, api_key, company_name):
        """
        Define a chave da API para o controlador CLI
        """
        self.api_key = api_key
        self.selected_company = company_name

        config = self.load_schedules() or {}
        config["selected_company"] = company_name
        config["api_key"] = api_key

        with open(self.config_path, "w", encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)

    def get_api_key(self):
        """
        Retorna a chave da API
        """
        if os.path.exists(self.keys_path):
            try:
                with open(self.keys_path, 'r', encoding='utf-8') as f:
                    keys = json.load(f)
                    if self.selected_company and self.selected_company in keys:
                        return keys[self.selected_company]
            except Exception:
                pass
        return None

    def _add_job(self, api_key, company, schedule_id, exec_time, selected_days, added_jobs, job_prefix=""):
        """
        Adiciona um trabalho ao scheduler

        Args:
            api_key (str): Chave da API
            company (str): Nome da empresa
            schedule_id (str): ID do agendamento
            exec_time (str): Horário de execução no formato HH:MM
            selected_days (list): Lista de dias selecionados
            added_jobs (set): Conjunto de jobs já adicionados
            job_prefix (str): Prefixo para o ID do job
        """
        hour, minute = map(int, exec_time.split(':'))
        trigger = CronTrigger(
            day_of_week=','.join(selected_days),
            hour=hour,
            minute=minute
        )

        # Cria um ID exclusivo para o "trabalho"
        job_id = f"{job_prefix}{schedule_id}_{hour}_{minute}"

        # Verifica se este trabalho já foi adicionado
        if job_id in added_jobs:
            return

        # Adiciona ao conjunto de trabalhos já processados
        added_jobs.add(job_id)

        # Cria uma função específica para este trabalho para evitar problemas
        def create_job_function(api_key_param, company_name_param):
            return lambda: self._run_export(api_key_param, company_name_param)

        job_function = create_job_function(api_key, company)

        self.scheduler.add_job(
            job_function,
            trigger=trigger,
            id=job_id,
            replace_existing=True
        )

    def _apply_saved_schedule(self):
        """
        Aplica a configuração salva do agendador
        """
        config = self.load_schedules()

        # Remove todos os trabalhos existentes
        self.scheduler.remove_all_jobs()

        # Conjunto para rastrear jobs já adicionados e evitar duplicatas
        added_jobs = set()

        # Verifica se há agendamentos no novo formato
        if config and "schedules" in config and config["schedules"]:
            # Aplica cada agendamento
            for schedule in config['schedules']:
                execution_times = schedule.get("execution_times", [])
                days = schedule.get("days", [False] * 7)
                api_key = schedule.get("api_key")
                company = schedule.get("selected_company")
                schedule_id = schedule.get("id")

                if not execution_times or not any(days):
                    continue

                # Obtém os dias como lista de nomes de dias
                day_names = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
                selected_days = [name for enabled, name in zip(days, day_names) if enabled]

                # Se não houver dias selecionados, não agendar
                if not selected_days:
                    continue

                # Adiciona os trabalhos para cada horário
                for i, exec_time in enumerate(execution_times):
                    self._add_job(api_key, company, f"{schedule_id}_{i}", exec_time, selected_days, added_jobs)

        # Compatibilidade com o formato antigo
        elif config and config.get('execution_times') and config.get('days'):
            execution_times = config.get('execution_times', [])
            days = config.get('days', [False] * 7)
            api_key = config.get('api_key')
            company = config.get('selected_company')

            # Obtém os dias como lista de nomes de dias
            day_names = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
            selected_days = [name for enabled, name in zip(days, day_names) if enabled]

            # Se não houver dias selecionados, não agendar
            if not selected_days:
                return

            # Adiciona os trabalhos para cada horário
            for i, exec_time in enumerate(execution_times):
                self._add_job(api_key, company, f"legacy_{i}", exec_time, selected_days, added_jobs)

    def _run_export(self, api_key=None, company_name=None):
        """
        Executa a operação de exportação

        Args:
            api_key (str): Chave de API a ser usada
            company_name (str): Nome da empresa a ser usada
        """
        # Usa um lock global para garantir que apenas uma thread execute esta seção por vez
        with SchedulerService._global_export_lock:
            # Variável que contém a data do último export
            current_time = datetime.now()

            # Verifica se a empresa já está sendo exportada
            if company_name in SchedulerService._company_exports:
                last_export_time = SchedulerService._company_exports[company_name]
                time_diff = (current_time - last_export_time).total_seconds()

                # Se a última exportação foi há menos de 2 minutos, ignora
                if time_diff < 120:
                    return

            # Registra esta exportação no dicionário de classe
            SchedulerService._company_exports[company_name] = current_time

            # Marca que uma exportação está em andamento para esta empresa
            if not hasattr(self, '_export_lock'):
                self._export_lock = {}
            self._export_lock[company_name] = True

            # Limpa exportações antigas (mais de 1 hora)
            for key in list(SchedulerService._company_exports.keys()):
                if (current_time - SchedulerService._company_exports[key]).total_seconds() > 3600:
                    del SchedulerService._company_exports[key]

        try:
            # Determina qual API key usar
            api_key_to_use = api_key

            # Se tiver company_name, tenta carregar a API key da empresa do arquivo keys.json
            if company_name and os.path.exists(self.keys_path):
                try:
                    with open(self.keys_path, 'r', encoding='utf-8') as f:
                        keys = json.load(f)
                        if company_name in keys:
                            api_key_to_use = keys[company_name]
                except Exception:
                    pass

            # Fallback final para a API key da instância
            if not api_key_to_use or api_key_to_use == "None":
                api_key_to_use = self.api_key

            class Args:
                pass

            args = Args()
            args.dt_final = datetime.now().strftime('%Y-%m-%d')
            args.dt_inicial = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            args.output_dir = os.path.join(os.path.expanduser('~'), 'Downloads')
            args.output_format = "xlsx"
            args.api_key = api_key_to_use
            args.company_name = company_name  # Passa o nome da empresa para o arquivo
            args.filter_empty_columns = True  # Reativa o filtro de colunas vazias

            try:
                controller = CLIController(api_key=args.api_key)
                success = controller.export_data(args)
            except Exception:
                success = False

            # Obtém o tray_icon para exibir notificações
            tray_icon = (SystemTrayIcon.get_instance() or
                         self.tray_icon or
                         (self.main_window.tray_icon if self.main_window and hasattr(self.main_window, 'tray_icon') else None))

            # Sempre usar notificação do tray icon quando disponível
            if tray_icon:
                if success:
                    tray_icon.showMessage(
                        "Pratiko",
                        t("Exportação realizada com sucesso."),
                        QSystemTrayIcon.Information,
                        5000
                        )

                else:
                    tray_icon.showMessage(
                        "Pratiko",
                        t("Exportação não pode ser realizada com sucesso."),
                        QSystemTrayIcon.Warning,
                        5000
                        )

        except Exception as e:
            # Obtém o tray_icon para exibir notificações
            tray_icon = (SystemTrayIcon.get_instance() or
                         self.tray_icon or
                         (self.main_window.tray_icon if self.main_window and hasattr(self.main_window, 'tray_icon') else None))

            if tray_icon:
                tray_icon.showMessage(
                    "Pratiko - Erro",
                    t("Falha na exportação agendada: {e}").format(e=str(e)),
                    QSystemTrayIcon.Warning,
                    5000
                )
            else:
                exibir_erro(None, t("Falha na exportação agendada: {e}").format(e=str(e)))
        finally:
            # Libera o bloqueio para esta empresa
            if hasattr(self, '_export_lock') and company_name in self._export_lock:
                self._export_lock[company_name] = False

    def save_schedule(self, execution_times: list, selected_days: list, schedule_id: str = None) -> bool:
        """
        Salva e aplica nova configuração do agendador.

        Args:
            execution_times (list): Lista de horários de execução
            selected_days (list): Lista de dias selecionados

        Returns:
            bool: True se salvo com sucesso, False caso contrário
        """
        try:
            # Formata cada objeto de tempo para string HH:MM
            formatted_times = []
            for time_item in execution_times:
                if hasattr(time_item, 'strftime'):
                    formatted_times.append(time_item.strftime('%H:%M'))
                elif isinstance(time_item, str):
                    formatted_times.append(time_item)
                elif hasattr(time_item, 'hour') and hasattr(time_item, 'minute'):
                    formatted_times.append(f"{time_item.hour:02d}:{time_item.minute:02d}")

            config = self.load_schedules() or {"schedules": []}

            if "schedules" not in config:
                config["schedules"] = []

            new_schedule = {
                'id': schedule_id
                or f"{self.selected_company}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'execution_times': formatted_times,
                'days': selected_days,
                'enabled': True,
                'last_modified': datetime.now().isoformat(),
                'api_key': self.api_key,
                'selected_company': self.selected_company
            }

            if schedule_id:
                config["schedules"] = [
                    s for s in config["schedules"] if s.get("id") != schedule_id
                ]

            config["schedules"].append(new_schedule)

            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)

            self._apply_saved_schedule()
            return True

        except Exception:
            return False

    def load_schedules(self):
        """
        Carrega todas as configurações do agendador do arquivo.
        Migra automaticamente configurações antigas para o novo formato.

        Returns:
            dict: Dicionário com todos os agendamentos ou um dicionário vazio com a chave 'schedules'
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                    # Migra configuração antiga para o novo formato
                    if "schedules" not in config and "execution_times" in config:
                        old_schedule = {
                            'id': f"legacy_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            'execution_times': config.get("execution_times", []),
                            'days': config.get("days", [False] * 7),
                            'enabled': config.get("enabled", True),
                            'last_modified': config.get("last_modified", datetime.now().isoformat()),
                            'api_key': config.get("api_key"),
                            'selected_company': config.get("selected_company")
                        }
                        config = {"schedules": [old_schedule]}

                        # Salva no novo formato
                        with open(self.config_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=4, ensure_ascii=False)

                    return config
            return {"schedules": []}

        except Exception:
            return {"schedules": []}

    def shutdown(self):
        if self.scheduler.running:
            self.scheduler.shutdown()
