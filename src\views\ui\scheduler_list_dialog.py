import json

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    Q<PERSON>ialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QWidget,
    QMessageBox,
)

from services.scheduler_service import SchedulerService
from services.language_utils.translations_functions import translate as t
from views.utils.widget_style import WidgetStyle
from views.ui.scheduler import SchedulerDialog
from views.utils.front_services import exibir_question, exibir_information


class SchedulerListDialog(QDialog):
    """
    Dialog para exibir e gerenciar os agendamentos existentes.
    """

    def __init__(self, parent=None, api_key=None, selected_company=None):
        """
        Inicializa o diálogo de lista de agendamentos.

        Args:
            parent (QWidget, optional): Widget pai. Defaults to None.
            api_key (str, optional): Chave de <PERSON>. Defaults to None.
        """
        super().__init__(parent)
        self.api_key = api_key
        self.selected_company = selected_company
        self.scheduler_service = SchedulerService(api_key=api_key)

        # Obtém o idioma atual da aplicação
        self.current_language = None
        parent_window = parent
        while parent_window:
            if hasattr(parent_window, 'current_language'):
                self.current_language = parent_window.current_language
                break
            parent_window = parent_window.parent()

        # Se não encontrou o idioma no parent, usa o idioma do sistema
        if not self.current_language:
            from services.language_utils.translations_functions import get_system_language
            self.current_language = get_system_language()

        # Força o idioma para Português se não for explicitamente English
        if self.current_language != "English":
            self.current_language = "Português"

        # Pega a empresa selecionada e sua key
        if selected_company:
            self.scheduler_service.set_api_key(api_key, selected_company)

        self.setup_ui()
        self.load_schedulers()

    def setup_ui(self):
        """
        Configura a interface do usuário.
        """
        # Configurações básicas da janela
        self.setWindowTitle(t("Agendamentos Existentes"))
        self.setFixedSize(800, 400)
        self.setStyleSheet(WidgetStyle().background_style())

        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Título
        title_label = QLabel(t("Agendamentos Configurados"))
        title_label.setStyleSheet("font-weight: bold; font-size: 14pt; color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Tabela de agendamentos
        self.table = QTableWidget()
        self.table.setColumnCount(4)

        # Seta o label da tabela para os headers
        headers = [t("Empresa"), t("Horários"), t("Dias"), t("Ações")]
        self.table.setHorizontalHeaderLabels(headers)

        # Centraliza os headers
        for i in range(len(headers)):
            self.table.horizontalHeaderItem(i).setTextAlignment(Qt.AlignCenter)

        # Tamanho/posicionamento das colunas
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)
        self.table.setColumnWidth(3, 200)

        # Tamanho da linha da tabela
        self.table.verticalHeader().setDefaultSectionSize(50)

        self.table.setStyleSheet(WidgetStyle().table_style())
        main_layout.addWidget(self.table)

        # Botões de ação
        button_layout = QHBoxLayout()

        self.add_button = QPushButton(t("Adicionar Novo"))
        self.add_button.clicked.connect(self.add_scheduler)

        self.close_button = QPushButton(t("Ok"))
        self.close_button.clicked.connect(self.accept)

        # Adiciona os botões ao layout
        button_layout.addWidget(self.add_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def load_schedulers(self):
        """
        Carrega os agendamentos existentes na tabela.
        """
        # Limpa a tabela
        self.table.setRowCount(0)

        # Carrega a configuração
        config = self.scheduler_service.load_schedules()
        if not config or "schedules" not in config:
            return

        for schedule in config["schedules"]:
            schedule_id = schedule.get("id")
            selected_company = schedule.get("selected_company")
            times = schedule.get("execution_times", [])
            days = schedule.get("days", [False] * 7)

            # Se não houver horários, não exibe nada
            if not times:
                continue

            # Adiciona uma linha na tabela
            row = self.table.rowCount()
            self.table.insertRow(row)

            # Empresa
            company_item = QTableWidgetItem(selected_company)
            company_item.setFlags(company_item.flags() & ~Qt.ItemIsEditable)
            company_item.setData(Qt.UserRole, schedule_id)
            company_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 0, company_item)

            # Horários - formata com base no idioma
            formatted_times = []
            for time_str in times:
                if self.current_language == "English":
                    # Converte de formato 24h para 12h com AM/PM
                    hour, minute = map(int, time_str.split(':'))
                    # Se os minutos forem zero, mostra apenas a hora (12 PM)
                    if minute == 0:
                        if hour == 0:
                            formatted_time = "12 AM"
                        elif hour < 12:
                            formatted_time = f"{hour} AM"
                        elif hour == 12:
                            formatted_time = "12 PM"
                        else:
                            formatted_time = f"{hour-12} PM"
                    else:
                        # Se tiver minutos, mostra hora e minutos (12:30 PM)
                        if hour == 0:
                            formatted_time = f"12:{minute:02d} AM"
                        elif hour < 12:
                            formatted_time = f"{hour}:{minute:02d} AM"
                        elif hour == 12:
                            formatted_time = f"12:{minute:02d} PM"
                        else:
                            formatted_time = f"{hour-12}:{minute:02d} PM"
                    formatted_times.append(formatted_time)
                else:
                    # Mantém o formato 24h para português
                    formatted_times.append(time_str)

            times_str = ", ".join(formatted_times)
            times_item = QTableWidgetItem(times_str)
            times_item.setFlags(times_item.flags() & ~Qt.ItemIsEditable)
            times_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 1, times_item)

            # Dias
            day_names = [t("Seg"), t("Ter"), t("Qua"), t("Qui"), t("Sex"), t("Sáb"), t("Dom")]
            selected_days = [name for name, selected in zip(day_names, days) if selected]
            days_str = ", ".join(selected_days) if selected_days else t("Nenhum")
            days_item = QTableWidgetItem(days_str)
            days_item.setFlags(days_item.flags() & ~Qt.ItemIsEditable)
            days_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, days_item)

            # Botões de ação
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(5, 5, 5, 5)
            action_layout.setSpacing(10)
            action_layout.setAlignment(Qt.AlignCenter)  # Centraliza os botões horizontalmente

            # Layout para o botão de editar
            edit_button = QPushButton(t("Editar"))
            edit_button.setFixedSize(80, 35)
            edit_button.setStyleSheet(WidgetStyle().edit_button_style())
            edit_button.clicked.connect(
                lambda _,
                sid=schedule_id,
                comp=selected_company: self.edit_scheduler(sid, comp))

            # Layout para o botão de excluir
            delete_button = QPushButton(t("Excluir"))
            delete_button.setFixedSize(80, 35)
            delete_button.setStyleSheet(WidgetStyle().delete_button_style())
            delete_button.clicked.connect(
                lambda _,
                sid=schedule_id,
                comp=selected_company: self.delete_scheduler(sid, comp))

            action_layout.addWidget(edit_button)
            action_layout.addWidget(delete_button)

            self.table.setCellWidget(row, 3, action_widget)

    def add_scheduler(self):
        """
        Adiciona um novo agendamento.
        """
        dialog = SchedulerDialog(
            self,
            api_key=self.api_key,
            company_name=self.selected_company
        )
        dialog.setWindowTitle(t("Novo Agendamento"))

        if dialog.exec():
            self.load_schedulers()

    def edit_scheduler(self, schedule_id, company):
        """
        Edita um agendamento existente.

        Args:
            schedule_id (str): ID do agendamento.
            company (str): Nome da empresa do agendamento.
        """
        # Dá load no agendamento
        config = self.scheduler_service.load_schedules()
        if not config or "schedules" not in config:
            return

        # Acha o agendamento pelo ID
        schedule = next((s for s in config["schedules"] if s.get("id") == schedule_id), None)
        if not schedule:
            return

        # Cria o diálogo
        dialog = SchedulerDialog(self, api_key=self.api_key, company_name=company)
        dialog.setWindowTitle(t("Editar Agendamento"))

        # Seta a empresa selecionada no diálogo
        if hasattr(dialog, 'company_combo') and company in dialog.available_companies:
            dialog.company_combo.setCurrentText(company)

        # Dá load nos horários e dias
        dialog.load_schedule_data(schedule)

        # Mostra o diálogo
        if dialog.exec():
            self.load_schedulers()

    def delete_scheduler(self, schedule_id, company):
        """
        Exclui um agendamento existente.

        Args:
            company (str): Nome da empresa do agendamento.
        """
        confirm = exibir_question(
            self,
            t(
                'Tem certeza de que deseja excluir o agendamento para "{company}"?').format(company=company)
        )

        if confirm == QMessageBox.Yes:
            # Limpa os horários e mantém os dias
            config = self.scheduler_service.load_schedules()
            if not config or "schedules" not in config:
                return

            # Remove o schedule
            config["schedules"] = [s for s in config["schedules"] if s.get("id") != schedule_id]

            # Salva a config atualizada
            with open(self.scheduler_service.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)

            # Aplica a nova configuração
            self.scheduler_service._apply_saved_schedule()

            exibir_information(self, t("Agendamento excluído com sucesso."))
            self.load_schedulers()
        else:
            exibir_information(self, t("Operação Cancelada."))
            return
