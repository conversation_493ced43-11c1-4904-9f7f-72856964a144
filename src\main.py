import sys
import os

from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QIcon

from views.pratiko_app import PratikoApp
from views.system_tray import SystemTrayIcon
from controllers.cli_controller import CLIController

from services.handlers.cli_args_handler import parse_arguments


def resolve_path(relative_path):
    """
    Resolve o caminho absoluto para arquivos ou diretórios relativos à localização do script.

    Resolve o caminho absoluto de um arquivo ou diretório relativo à localização do script.
    Isso é particularmente útil quando o script é empacotado em um único executável usando PyInstaller.
    """
    if hasattr(sys, "_MEIPASS"):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, relative_path)


def get_executable_dir():
    """
    Pega o diretório do executável.

    Retorna o diretório do executável, considerando se o script está sendo executado como um executável empacotado ou não.
    Isso é útil para localizar arquivos de recursos, como ícones ou arquivos de configuração, que estão no mesmo diretório que o executável.
    """
    if hasattr(sys, "_MEIPASS"):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))


def run_gui():
    """
    Executa a interface gráfica da aplicação.
    """
    # Setando variável de ambiente para desabilitar o scaling de DPI, pois estava dando erro
    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "0"

    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon(resolve_path("assets/Pratiko.ico")))

    executable_dir = get_executable_dir()
    api_keys_path = os.path.join(executable_dir, "keys.json")

    window = PratikoApp(api_keys_path)
    window.show()

    # Cria e configura o ícone da bandeja do sistema
    tray = SystemTrayIcon(app.windowIcon(), app, window)
    tray.show()
    window.tray_icon = tray

    # Atualiza as referências do scheduler service
    window.update_scheduler_references()

    return app.exec()


def run_cli(args):
    """
    Dá run na aplicação em modo CLI, caso necessário.
    """
    controller = CLIController(api_key=args.api_key)
    success = controller.export_data(args)
    return 0 if success else 1


if __name__ == "__main__":
    # Verifica se está rodando em modo linha de comando
    if len(sys.argv) > 1:
        args = parse_arguments()
        config_path = os.path.join(get_executable_dir(), "keys.json")
        controller = CLIController(config_path=config_path)
        success = controller.export_data(args)
        sys.exit(0 if success else 1)
    else:
        # Importa e inicializa componentes GUI apenas quando necessário
        sys.exit(run_gui())
